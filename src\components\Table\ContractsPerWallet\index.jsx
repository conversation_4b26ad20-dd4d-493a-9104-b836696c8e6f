import React from "react"
import 'antd/dist/antd.css'
import '../../../styles/table.css'
import { Table } from 'antd'
import { FilterOutlined } from '@ant-design/icons'


const columns = [
  {
    dataIndex: 'priority',
    key: 'priority',
    render: (color) => (
      <div className="priority-container">
        <div className="priority" style={{ backgroundColor: color }}></div>
      </div>
    ),
  },
	{
  	title: 'Data de vencimento',
    dataIndex: 'expirationDate',
    key: 'expirationDate',
    render: (expirationDate) => <p className="table-font">{expirationDate}</p>,
  },
	{
  	title: 'Nome do contrato',
  	dataIndex: 'contract',
    key: 'contract',
    render: (contract) => <p className="table-font">{contract}</p>,
  },
	{
  	title: 'Cliente',
    dataIndex: 'client',
    key: 'client',
		render: (client) => <p className="table-font">{client}</p>,
  },
	{
		title: 'Tipo',
		dataIndex: 'type',
		key: 'type',
		render: (type) => <p className="table-font">{type}</p>,
	},
  {
    title: 'Total',
    dataIndex: 'total',
    key: 'total',
    render: (total) => <p className="table-font">{total}</p>,
	},
	{
		title: 'Total Mensal',
		dataIndex: 'monthTotal',
		key: 'monthTotal',
		render: (monthTotal) => <p className="table-font">{monthTotal}</p>
	}
];

const data = [
  {
		key: '1',
		priority: '#43914F',
		expirationDate: '30/06/2022',
		contract: 'Contrato',
		client: 'Darede',
		type: 'Semestral',
		total: 40,
		monthTotal: 3,
	},
	{
		key: '2',
		priority: '#F3DC0B',
		expirationDate: '30/06/2022',
		contract: 'Contrato',
		client: 'Darede',
		type: 'Semestral',
		total: 40,
		monthTotal: 3,
	},
	{
		key: '3',
		priority: '#EA2424',
		expirationDate: '30/06/2022',
		contract: 'Contrato',
		client: 'Logikee',
		type: 'Semestral',
		total: 40,
		monthTotal: 3,
	},
];

function ContractPerWalletTable(props) {

	return (
		<div>
				<div className="table-card-top">
					<div className="table-filter-content">
						<button>
							<FilterFilled className="table-filter-icon"/>
						</button>
          </div>
					<div className="button">
						<button className="btn-hidden" onClick={() => props.showModal()}>
							Esconder
						</button>
					</div>
				</div>
				<div className="container-table">
						<Table columns={columns} dataSource={data} />
				</div>
		</div>
	)
}

export default ContractPerWalletTable