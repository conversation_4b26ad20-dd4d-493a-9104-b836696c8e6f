import { Popover, Table } from "antd";
import React, { useEffect, useState } from "react";
import "antd/dist/antd.css";
import "../../../styles/table.css";
import { FilterFilled } from "@ant-design/icons";
import ReservedHoursFilter from "../../Modals/Filters/ReservedHoursFilter";

const columns = [
  {
    title: "Cliente",
    dataIndex: "clientName",
    key: "clientName",
    render: (clientName) => <p className="table-font">{clientName}</p>,
  },
  {
    title: "Nome do contrato",
    dataIndex: "contractName",
    key: "contractName",
    render: (contractName) => <p className="table-font">{contractName}</p>,
  },
  {
    title: "Tipo de contrato",
    dataIndex: "type",
    key: "type",
    render: (type) => <p className="table-font">{type}</p>,
  },
  {
    title: "Carte<PERSON>",
    dataIndex: "wallet",
    key: "wallet",
    render: (wallet) => <p className="table-font">{wallet}</p>,
  },
  {
    title: "Total mensal",
    dataIndex: "cosumedHours",
    key: "cosumedHours",
    render: (cosumedHours) => <p className="table-font">{cosumedHours}</p>,
  },
];

function ConsumedHoursPerMonthTable({
  allTicketsData,
  filteredData,
  allClients,
  allWallets,
  filteredContracts,
  getFilteredContracts,
  getFilteredContractByClient,
  getGraphData,
  showModal,
}) {
  const [popoverVisibility, setPopoverVisibility] = useState(false);
  const [dataTable, setDataTable] = useState([]);

  function handlePopoverVisibility() {
    setPopoverVisibility(!popoverVisibility);
  }

  function handleDataTable() {
    let tableData = [];
    // ✅ Corrigido: usar forEach() em vez de map() para efeitos colaterais
    filteredData.forEach((contract) => {
      tableData.push({
        wallet: contract.document_id,
        contractName: contract.name,
        clientName: ``,
        cosumedHours: 0,
        type: "-",
        contractId: contract.id,
        clientId: contract.customer_id,
      });
    });

    // ✅ Corrigido: usar forEach() em vez de map()
    allClients.forEach((client) => {
      for (let contract of tableData) {
        if (client[1].customer_id === contract.clientId) {
          contract.clientName = client[1].name;
        }
      }
    });

    // ✅ Corrigido: usar forEach() em vez de map()
    allTicketsData.forEach((month) => {
      month.forEach((contract) => {
        for (let client of tableData) {
          if (contract.contract_id === client.contractId) {
            client.cosumedHours += contract.uv;
          }
        }
      });
    });

    setDataTable(tableData);
  }

  useEffect(() => {
    handleDataTable();
  }, [showModal, filteredData, allClients, allTicketsData]); // ✅ Adicionadas todas as dependências necessárias

  return (
    <div>
      <div className="table-card-top">
        <div className="table-filter">
          <Popover
            open={popoverVisibility}
            onClick={handlePopoverVisibility}
            placement="left"
            content={() => {
              return (
                <ReservedHoursFilter
                  allTicketsData={allTicketsData}
                  filteredData={filteredData}
                  allClients={allClients}
                  allWallets={allWallets}
                  filteredContracts={filteredContracts}
                  getFilteredContracts={getFilteredContracts}
                  getFilteredContractByClient={getFilteredContractByClient}
                  getGraphData={getGraphData}
                  handlePopoverVisibility={handlePopoverVisibility}
                />
              );
            }}
            trigger="click"
          >
            <div className="table-filter-content">
              <FilterFilled className="table-filter-icon" />
            </div>
          </Popover>
        </div>
      </div>
      <div className="container-table">
        <Table columns={columns} dataSource={dataTable} />
      </div>
    </div>
  );
}

export default ConsumedHoursPerMonthTable;
