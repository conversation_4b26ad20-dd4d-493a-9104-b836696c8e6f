/**
 * AuthProvider EXCLUSIVAMENTE HttpOnly
 * Implementação 100% segura sem Bearer tokens
 */

import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { pureHttpOnlyAuthService } from '../services/pureHttpOnlyAuthService';
import { logger } from '../utils/logger';
import { AUTH_STATUS, AUTH_ERRORS, AUTH_ROUTES } from '../constants/auth';

/**
 * Contexto de autenticação HttpOnly
 */
const PureHttpOnlyAuthContext = createContext(null);

/**
 * AuthProvider EXCLUSIVAMENTE com cookies HttpOnly
 */
export const PureHttpOnlyAuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [error, setError] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [lastActivity, setLastActivity] = useState(Date.now());
  const [sessionWarning, setSessionWarning] = useState(false);

  /**
   * Inicializar serviço HttpOnly
   */
  useEffect(() => {
    const initializeHttpOnlyAuth = async () => {
      try {
        setIsLoading(true);
        setError(null);

        logger.info('🔒 Inicializando autenticação HttpOnly exclusiva');

        // Configurar interceptors HttpOnly
        const cleanup = pureHttpOnlyAuthService.configureHttpOnlyInterceptors();

        // Verificar autenticação existente
        await checkCurrentAuth();

        setIsInitialized(true);
        
        return cleanup;
      } catch (error) {
        logger.error('❌ Erro na inicialização HttpOnly', { error: error.message });
        setError(error.message);
        setIsInitialized(true);
      } finally {
        setIsLoading(false);
      }
    };

    const cleanup = initializeHttpOnlyAuth();
    
    return () => {
      if (cleanup && typeof cleanup === 'function') {
        cleanup();
      }
    };
  }, []);

  /**
   * Verificar autenticação atual via cookies HttpOnly
   */
  const checkCurrentAuth = useCallback(async () => {
    try {
      setError(null);

      const isAuth = await pureHttpOnlyAuthService.isAuthenticated();
      
      if (isAuth) {
        const userData = await pureHttpOnlyAuthService.getCurrentUser();
        setUser(userData);
        setIsAuthenticated(true);
        
        // logger.info('✅ Usuário autenticado via cookies HttpOnly', { 
        //   user: userData.email 
        // });
      } else {
        setUser(null);
        setIsAuthenticated(false);
        
        logger.debug('❌ Usuário não autenticado');
      }

    } catch (error) {
      logger.error('❌ Erro na verificação de autenticação', { error: error.message });
      setError(error.message);
      setIsAuthenticated(false);
      setUser(null);
    }
  }, []);

  /**
   * Login com código OAuth via cookies HttpOnly
   */
  const loginWithCode = useCallback(async (code) => {
    try {
      setIsLoading(true);
      setError(null);

      // logger.info('🔐 Iniciando login via cookies HttpOnly', { 
      //   codeLength: code.length 
      // });

      const result = await pureHttpOnlyAuthService.authenticate(code);
      
      setUser(result.user);
      setIsAuthenticated(true);
      setLastActivity(Date.now());

      logger.info('✅ Login via cookies HttpOnly bem-sucedido', { 
        user: result.user.email 
      });

      return result;

    } catch (error) {
      logger.error('❌ Erro no login via cookies HttpOnly', { error: error.message });
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Logout via cookies HttpOnly
   */
  const logoutHttpOnly = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // logger.info('🚪 Iniciando logout via cookies HttpOnly');

      await pureHttpOnlyAuthService.logoutHttpOnly();

      setIsAuthenticated(false);
      setUser(null);
      setError(null);
      setSessionWarning(false);

      // logger.info('✅ Logout via cookies HttpOnly concluído');

    } catch (error) {
      logger.error('❌ Erro no logout via cookies HttpOnly', { error: error.message });
      
      // Mesmo com erro, limpar estado local
      setIsAuthenticated(false);
      setUser(null);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh token via cookies HttpOnly
   */
  const refreshToken = useCallback(async () => {
    try {
      await pureHttpOnlyAuthService.refreshTokenHttpOnly();
      setLastActivity(Date.now());
      setSessionWarning(false);
      
      // logger.info('🔄 Token refreshed via cookies HttpOnly');
      return true;
    } catch (error) {
      logger.error('❌ Erro no refresh via cookies HttpOnly', { error: error.message });
      return false;
    }
  }, []);

  /**
   * Verificar se usuário tem permissão
   */
  const hasPermission = useCallback((permission) => {
    if (!user || !permission) return false;
    
    return user.permissions?.includes(permission) || 
           user.groups?.includes(permission) || 
           false;
  }, [user]);

  /**
   * Verificar se usuário tem qualquer uma das permissões
   */
  const hasAnyPermission = useCallback((permissions = []) => {
    if (!user || !permissions.length) return false;
    
    return permissions.some(permission => hasPermission(permission));
  }, [user, hasPermission]);

  /**
   * Verificar se usuário tem todas as permissões
   */
  const hasAllPermissions = useCallback((permissions = []) => {
    if (!user || !permissions.length) return false;
    
    return permissions.every(permission => hasPermission(permission));
  }, [user, hasPermission]);

  /**
   * Atualizar atividade do usuário
   */
  const updateActivity = useCallback(() => {
    setLastActivity(Date.now());
    setSessionWarning(false);
  }, []);

  /**
   * Monitorar atividade do usuário
   */
  useEffect(() => {
    if (!isAuthenticated) return;

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      updateActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [isAuthenticated, updateActivity]);

  /**
   * Verificar inatividade
   */
  useEffect(() => {
    if (!isAuthenticated) return;

    const inactivityTimeout = 25 * 60 * 1000; // 25 minutos
    const warningTimeout = 23 * 60 * 1000; // 23 minutos

    const checkInactivity = () => {
      const now = Date.now();
      const timeSinceActivity = now - lastActivity;

      if (timeSinceActivity >= inactivityTimeout) {
        logger.warn('⏰ Sessão expirada por inatividade');
        logoutHttpOnly();
      } else if (timeSinceActivity >= warningTimeout && !sessionWarning) {
        setSessionWarning(true);
        logger.warn('⚠️ Aviso de inatividade');
      }
    };

    const interval = setInterval(checkInactivity, 60000); // Verificar a cada minuto

    return () => clearInterval(interval);
  }, [isAuthenticated, lastActivity, sessionWarning, logoutHttpOnly]);

  /**
   * Valor do contexto memoizado
   */
  const contextValue = useMemo(() => ({
    isAuthenticated,
    isLoading,
    user,
    error,
    isInitialized,
    
    sessionWarning,
    lastActivity,
    
    // Métodos de autenticação
    login: loginWithCode,
    logout: logoutHttpOnly,
    checkAuth: checkCurrentAuth,
    refreshToken,
    
    // Helpers de usuário
    userEmail: user?.email,
    userName: user?.name || user?.username,
    userInitials: user?.name ? 
      user.name.split(' ').map(n => n[0]).join('').toUpperCase() :
      user?.email?.substring(0, 2).toUpperCase(),
    
    // Helpers de permissão
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    
    // Helpers de atividade
    updateActivity,
    extendSession: updateActivity,
    
    // Status helpers
    isUnauthenticated: !isAuthenticated && !isLoading,
    status: isLoading ? AUTH_STATUS.LOADING : 
            isAuthenticated ? AUTH_STATUS.AUTHENTICATED : 
            AUTH_STATUS.UNAUTHENTICATED,
    
    // Configuração
    authMethod: 'httponly',
    httpOnlySupported: true,
    bearerTokens: false,
    
    AUTH_STATUS,
    AUTH_ERRORS,
    AUTH_ROUTES
  }), [
    isAuthenticated,
    isLoading,
    user,
    error,
    isInitialized,
    sessionWarning,
    lastActivity,
    loginWithCode,
    logoutHttpOnly,
    checkCurrentAuth,
    refreshToken,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    updateActivity
  ]);

  return (
    <PureHttpOnlyAuthContext.Provider value={contextValue}>
      {children}
    </PureHttpOnlyAuthContext.Provider>
  );
};

/**
 * Hook para usar o contexto de autenticação HttpOnly
 */
export const usePureHttpOnlyAuth = () => {
  const context = useContext(PureHttpOnlyAuthContext);
  
  if (!context) {
    throw new Error('usePureHttpOnlyAuth deve ser usado dentro de um PureHttpOnlyAuthProvider');
  }
  
  return context;
};

export default PureHttpOnlyAuthContext;
