import axios from "axios";
import { authService } from "../../services/authService";
import { getApiUrl } from "../../utils/devConfig";

export const filterBySearch = (data, search) => {
  if (!data || !search) {
    return [];
  }

  const searchLower = search.toLowerCase();

  const filteredData = data.filter((item) => {
    if (item.accounts?.length > 0) {
      if (
        item.accounts.some((account) =>
          account.account_id.includes(searchLower)
        )
      ) {
        return true;
      }
    }
    if (
      item.identifications?.itsm_id &&
      item.identifications.itsm_id
        .toString()
        .toLowerCase()
        .includes(searchLower)
    ) {
      return true;
    }

    if (
      item.dsm_id &&
      item.dsm_id.toString().toLowerCase().includes(searchLower)
    ) {
      return true;
    }

    if (item.cnpj && item.cnpj.toString().toLowerCase().includes(searchLower)) {
      return true;
    }

    if (
      item.names?.name &&
      item.names.name.toLowerCase().includes(searchLower)
    ) {
      return true;
    }

    if (item.name && item.name.toLowerCase().includes(searchLower)) {
      return true;
    }

    if (
      item.names?.fantasy_name &&
      item.names.fantasy_name.toLowerCase().includes(searchLower)
    ) {
      return true;
    }

    return false;
  });

  return filteredData;
};

export const filterContracts = (contracts, customer, state) => {
  if (!contracts || !customer || !state) return [];
  switch (state) {
    case "all":
      return contracts?.filter(
        (contract) =>
          (contract?.customer?.id || contract.customer?.itsm_id) &&
          (contract?.customer?.id?.toString() === customer?.id?.toString() ||
            contract.customer?.itsm_id?.toString() ===
              customer.identifications?.itsm_id?.toString())
      );

    case "ativosA":
      return contracts?.filter(
        (contract, index, array) =>
          (contract?.customer?.id || contract.customer?.itsm_id) &&
          (contract?.customer?.id?.toString() === customer?.id?.toString() ||
            contract.customer?.itsm_id?.toString() ===
              customer.identifications?.itsm_id?.toString()) &&
          array
            ?.filter(
              (contract) =>
                (contract?.customer?.id || contract.customer?.itsm_id) &&
                (contract?.customer?.id?.toString() ===
                  customer?.id?.toString() ||
                  contract.customer?.itsm_id?.toString() ===
                    customer.identifications?.itsm_id?.toString())
            )
            .some((x) => x.active)
      );

    case "ativosI":
      return contracts?.filter(
        (contract, index, array) =>
          (contract?.customer?.id || contract.customer?.itsm_id) &&
          (contract?.customer?.id?.toString() === customer?.id?.toString() ||
            contract.customer?.itsm_id?.toString() ===
              customer.identifications?.itsm_id?.toString()) &&
          array
            ?.filter(
              (contract) =>
                (contract?.customer?.id || contract.customer?.itsm_id) &&
                (contract?.customer?.id?.toString() ===
                  customer?.id?.toString() ||
                  contract.customer?.itsm_id?.toString() ===
                    customer.identifications?.itsm_id?.toString())
            )
            .every((x) => !x.active)
      );

    case "prospect":
      return !contracts?.filter(
        (contract, index, array) =>
          (contract?.customer?.id || contract.customer?.itsm_id) &&
          (contract?.customer?.id?.toString() === customer?.id?.toString() ||
            contract.customer?.itsm_id?.toString() ===
              customer.identifications?.itsm_id?.toString())
      ).length;

    default:
      break;
  }
};

export const filterByState = (data, state, contracts) => {
  if (!data || !state) return [];
  switch (state) {
    case "ativosA":
      return data?.filter(
        (e) =>
          e.active === 1 &&
          filterContracts(contracts?.data, e, "ativosA")?.length
      );
    case "inativosC":
      return data?.filter((e) => e?.active === 0);
    case "ativosI":
      return data?.filter(
        (e) =>
          e?.active === 1 &&
          filterContracts(contracts?.data, e, "ativosI")?.length
      );
    case "prospect":
      return data?.filter(
        (e) =>
          e?.active === 1 && filterContracts(contracts?.data, e, "prospect")
      );
    case "todos":
    default:
      return data;
  }
};

export const getCustomerContracts = async (customerId) => {
  try {
    const apiUrl = getApiUrl();
    const { data } = await axios.get(`${apiUrl}/contracts`, {
      params: {
        customer_id: customerId,
      },
    });
    );
    return data;
  } catch (error) {
    console.log(error);
  }
};

export const getCustomerByParameter = async (route, params) => {
  try {
    const apiUrl = getApiUrl();
    const customers = await axios.get(`${apiUrl}/${route}`, {
      params: params,
      headers: {
        dynamodb: `${process.env.REACT_APP_STAGE}-customers`,
      },
    });
    );

    return customers.data.data.Items;
  } catch (error) {
    console.log(error);
  }
};

export async function getAllCustomers() {
  let { data, nextPage } = await getPaginateCustomers();
  while (nextPage) {
    let response = await getPaginateCustomers(nextPage);
    nextPage = response.nextPage;
    data.push(...response.data);
  }

  return {
    Items: data,
  };
}

async function getPaginateCustomers(nextPage = "") {
  let params = [];
  if (nextPage) params.push(`?nextPage=${nextPage}`);

  const apiUrl = getApiUrl();
  const { data } = await axios.get(`${apiUrl}/read/paginate${params.join("&")}`, {
    headers: {
      dynamodb: `${process.env.REACT_APP_STAGE}-customers`,
    },
  });
  );

  return {
    data: data.data,
    nextPage: data.nextPage,
  };
}

export async function getCustomers() {
  let { customers, nextPage } = await getCustomerPaginate();

  while (nextPage) {
    let response = await getCustomerPaginate(nextPage);
    nextPage = response.nextPage;
    customers.push(...response.customers);
  }

  return { Items: customers };
}

async function getCustomerPaginate(nextPage = "") {
  const jwt = localStorage.getItem("jwt");

  let params = [];
  if (nextPage) params.push(`?nextPage=${nextPage}`);

  const { data } = await axios.get(
    `${process.env.REACT_APP_API_PERMISSION}read/customers${params.join("&")}`,
    {
      headers: {
        Authorization: jwt,
      },
    }
  );
  return {
    customers: data.data.customers,
    nextPage: data.data.nextPage,
  };
}

export async function getAllCustomersV2() {
  try {
    const custumersPromise = await Promise.allSettled([
      getActiveCustomers(),
      getInativeCustomers(),
    ]);
    const customers = custumersPromise.flatMap((c) => c.value);
    return customers;
  } catch (error) {
    console.log(error);
  }
}

async function getActiveCustomers() {
  const route = "customers/read/status";
  const customers = await getCustomerByParameter(route, {
    status: 1,
  });
  return customers;
}

async function getInativeCustomers() {
  const route = "customers/read/status";
  const customers = await getCustomerByParameter(route, {
    status: 0,
  });
  return customers;
}
