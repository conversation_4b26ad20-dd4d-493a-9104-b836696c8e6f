/**
 * Utilitários para testar autenticação HttpOnly
 * Ferramentas de debug e verificação
 */

import axios from 'axios';
import { config } from './validateEnvironment';
import { getApiUrl } from './devConfig';

/**
 * Testar conectividade com a API
 */
export const testApiConnectivity = async () => {
  try {
    const apiUrl = getApiUrl();
    console.log('🔍 Testando conectividade com a API...');
    console.log('URL da API:', apiUrl);
    console.log('🔧 Testando proxy:', apiUrl.startsWith('/api') ? 'SIM' : 'NÃO');

    const response = await axios.get(`${apiUrl}/health`, {
      timeout: 5000,
      withCredentials: false // Health check não precisa de auth
    });
    
    console.log('✅ API acessível:', response.status);
    return true;
  } catch (error) {
    console.log('❌ API inacessível:', error.message);
    return false;
  }
};

/**
 * Testar autenticação via cookies HttpOnly
 */
export const testHttpOnlyAuth = async () => {
  try {
    const apiUrl = getApiUrl();
    console.log('🍪 Testando autenticação via cookies HttpOnly...');

    // Primeiro, vamos testar um endpoint simples que não precisa de auth
    console.log('🔍 Testando endpoint público primeiro...');

    const response = await axios.get(`${apiUrl}/auth/verify`, {
      withCredentials: true,
      timeout: 10000
    });
    
    console.log('✅ Autenticação HttpOnly funcionando:', response.status);
    return response.data;
  } catch (error) {
    console.log('❌ Autenticação HttpOnly falhou:', {
      status: error.response?.status,
      message: error.message,
      data: error.response?.data
    });
    return null;
  }
};

/**
 * Testar acesso ao endpoint /cognito/read
 */
export const testCognitoRead = async () => {
  try {
    const apiUrl = getApiUrl();
    console.log('👥 Testando acesso ao /cognito/read...');

    const response = await axios.get(`${apiUrl}/cognito/read`, {
      withCredentials: true,
      timeout: 15000
    });
    
    console.log('✅ /cognito/read funcionando:', {
      status: response.status,
      userCount: response.data?.data?.length || 0
    });
    return response.data;
  } catch (error) {
    console.log('❌ /cognito/read falhou:', {
      status: error.response?.status,
      message: error.message,
      data: error.response?.data
    });
    return null;
  }
};

/**
 * Verificar configurações do axios
 */
export const checkAxiosConfig = () => {
  console.log('🔧 Verificando configurações do axios...');
  
  const config = {
    defaults: {
      withCredentials: axios.defaults.withCredentials,
      timeout: axios.defaults.timeout,
      headers: axios.defaults.headers.common
    },
    interceptors: {
      request: axios.interceptors.request.handlers.length,
      response: axios.interceptors.response.handlers.length
    }
  };
  
  console.log('Configurações do axios:', config);
  return config;
};

/**
 * Verificar cookies no navegador
 */
export const checkBrowserCookies = () => {
  console.log('🍪 Verificando cookies no navegador...');
  
  const cookies = document.cookie.split(';').reduce((acc, cookie) => {
    const [name, value] = cookie.trim().split('=');
    if (name) acc[name] = value;
    return acc;
  }, {});
  
  console.log('Cookies visíveis:', cookies);
  
  // Verificar se há tokens no localStorage (não deveria ter)
  const localStorageTokens = [];
  const tokenKeys = ['jwt', 'token', 'authToken', '@dsm/token', 'access_token', 'id_token'];
  
  tokenKeys.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorageTokens.push(key);
    }
  });
  
  if (localStorageTokens.length > 0) {
    console.warn('⚠️ Tokens encontrados no localStorage (INSEGURO):', localStorageTokens);
  } else {
    console.log('✅ localStorage limpo (SEGURO)');
  }
  
  return {
    visibleCookies: cookies,
    localStorageTokens
  };
};

export const testBackendDirect = async () => {
  try {
    console.log('🔧 Testando backend diretamente (sem proxy)...');

    // Testar diretamente o backend real
    const directResponse = await axios.get('https://api.dsm.darede.com.br/dev/health', {
      timeout: 5000,
      withCredentials: false // Sem cookies para teste básico
    });

    console.log('✅ Backend direto funcionando:', directResponse.status);
    return { status: 'success', data: directResponse.status };
  } catch (error) {
    console.log('❌ Backend direto falhou:', error.message);
    return { status: 'error', message: error.message };
  }
};

/**
 * Executar todos os testes
 */
export const runAllAuthTests = async () => {
  console.log('🧪 Executando todos os testes de autenticação...');
  console.log('='.repeat(50));

  const results = {
    apiConnectivity: await testApiConnectivity(),
    axiosConfig: checkAxiosConfig(),
    browserCookies: checkBrowserCookies(),
    httpOnlyAuth: await testHttpOnlyAuth(),
    cognitoRead: await testCognitoRead(),
    backendDirect: await testBackendDirect()
  };
  
  console.log('='.repeat(50));
  console.log('📊 Resultados dos testes:', results);
  
  // Resumo
  const summary = {
    apiAccessible: results.apiConnectivity,
    authWorking: !!results.httpOnlyAuth,
    cognitoWorking: !!results.cognitoRead,
    backendWorking: results.backendDirect?.status === 'success',
    securityOk: results.browserCookies.localStorageTokens.length === 0,
    interceptorsConfigured: results.axiosConfig.interceptors.request > 0
  };
  
  console.log('📋 Resumo:', summary);
  
  if (summary.apiAccessible && summary.authWorking && summary.cognitoWorking && summary.securityOk) {
    console.log('🎉 TODOS OS TESTES PASSARAM - Sistema funcionando corretamente!');
  } else {
    console.log('❌ Alguns testes falharam - Verificar configurações');
  }
  
  return { results, summary };
};

/**
 * Debug de requisição específica
 */
export const debugRequest = async (url, options = {}) => {
  console.log('🔍 Debug de requisição:', { url, options });
  
  try {
    const response = await axios({
      url,
      method: 'GET',
      withCredentials: true,
      timeout: 10000,
      ...options
    });
    
    console.log('✅ Requisição bem-sucedida:', {
      status: response.status,
      headers: response.headers,
      data: response.data
    });
    
    return response;
  } catch (error) {
    console.log('❌ Requisição falhou:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      headers: error.response?.headers,
      data: error.response?.data,
      message: error.message
    });
    
    throw error;
  }
};

export default {
  testApiConnectivity,
  testHttpOnlyAuth,
  testCognitoRead,
  checkAxiosConfig,
  checkBrowserCookies,
  runAllAuthTests,
  debugRequest
};
