import { <PERSON>ton, Col, Popover, Row, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import "../../../styles/table.css";
import "antd/dist/antd.css";
import { FilterFilled } from "@ant-design/icons";
import moment from "moment";
import { otrsGet } from "../../../service/apiOtrs";
import { dynamoGet } from "../../../service/apiDsmDynamo";
import ExpireHoursFilter from "../../Modals/Filters/ExpireHoursFilter";
import { SearchInput } from "../../SearchInput";
import {
  getUpdatedContractHours,
  groupBy,
  renderTotalHoursByITSM,
} from "../../../controllers/cockpit/cockpitController";

function TableExpire(props) {
  const { data } = props;
  const { Title, Text } = Typography;
  const [allCustomers, setAllCustomers] = useState([]);
  const [allContractTypes, setAllContractTypes] = useState([]);
  const [contractWithChangedHours, setContractWithChangedHours] = useState([]);
  const [filteredData, setFilteredData] = useState(data?.contracts);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadData = async () => {
      let changedHoursData = await getUpdatedContractHours(
        data?.contracts,
        "itsm_id",
        "id"
      );
      console.log(changedHoursData);
      setContractWithChangedHours(changedHoursData);
    };

    loadData();
  }, [props]);

  const columns = [
    {
      title: "Cliente",
      dataIndex: "customer_id",
      key: "client",
      render: (customer_id) => <Text>{getCustomerName(customer_id)}</Text>,
    },
    {
      title: "Nome do contrato",
      dataIndex: "name",
      key: "name",
      render: (name) => <Text>{name}</Text>,
    },
    {
      title: "Tipo de contrato",
      dataIndex: "type_id",
      key: "type",
      render: (type_id) => <Text>{getContractTypeName(type_id)}</Text>,
    },
    {
      title: "Carteira",
      dataIndex: "document_id",
      key: "wallet",
      render: (document_id) => <Text>{document_id}</Text>,
    },
    {
      title: "Total de horas",
      dataIndex: "total_hours",
      key: "totalHours",
      render: (total_hours, data) => {
        if (contractWithChangedHours.length > 0) {
          if (
            contractWithChangedHours.find(
              (contract) => contract.itsm_id == data.id
            )
          ) {
            return contractWithChangedHours?.find(
              (contract) => contract.itsm_id == data.id
            )?.new_hours;
          }
        }
        return <Text>{total_hours}</Text>;
      },
    },
    {
      title: "Horas gastas do projeto",
      dataIndex: "cumulative_hours",
      key: "cumulativeHours",
      render: (cumulative_hours) => <Text>{cumulative_hours}</Text>,
    },
    {
      title: "Data de expiração",
      dataIndex: "end_date",
      key: "endDate",
      render: (end_date) => (
        <Text>{moment(end_date).format("DD/MM/YYYY")}</Text>
      ),
    },
  ];

  function getAllCustomer() {
    setLoading(true);
    otrsGet("read/customer/all/0")
      .then((res) => {
        const { data } = res;
        setAllCustomers(data);
        setLoading(false);
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
      });
  }

  function getCustomerName(id) {
    let customerName = "-";

    Object.entries(allCustomers).forEach((item) => {
      const customer = item[1];
      if (customer.customer_id === id) {
        customerName = customer.name;
      }
    });

    return customerName;
  }

  function getAllContractTypes() {
    dynamoGet("hml-contract-type")
      .then((res) => {
        setAllContractTypes(res);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  function getContractTypeName(typeId) {
    const contractType = allContractTypes.filter(
      (item) => typeId === item.type
    );

    return contractType.length === 0 ? "-" : contractType[0].name;
  }

  useEffect(() => {
    getAllCustomer();
    getAllContractTypes();
  }, []);

  useEffect(() => {
    setFilteredData(data?.contracts);
  }, [data]);

  return (
    <Row gutter={[16, 16]}>
      <Col span={12}>
        <SearchInput
          placeholder="Pesquisar"
          onChange={(value) =>
            setFilteredData(
              data?.contracts.filter((item) =>
                item.name.toLowerCase().includes(value.toLowerCase())
              )
            )
          }
        />
      </Col>
      <Col span={24}>
        <Table loading={loading} columns={columns} dataSource={filteredData} />
      </Col>
    </Row>
  );
}

export default TableExpire;
