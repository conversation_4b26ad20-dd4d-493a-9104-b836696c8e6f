/**
 * Mock para api.js
 * Evita problemas com interceptors durante os testes
 */

import axios from 'axios';

// Mock das instâncias de API
export const apiDsm = {
  ...axios.create(),
  interceptors: {
    request: {
      use: jest.fn()
    },
    response: {
      use: jest.fn()
    }
  }
};

export const apiProposals = {
  ...axios.create(),
  interceptors: {
    request: {
      use: jest.fn()
    },
    response: {
      use: jest.fn()
    }
  }
};

export const apiOTRS = {
  ...axios.create(),
  interceptors: {
    request: {
      use: jest.fn()
    },
    response: {
      use: jest.fn()
    }
  }
};

export const apiReports = {
  ...axios.create(),
  interceptors: {
    request: {
      use: jest.fn()
    },
    response: {
      use: jest.fn()
    }
  }
};

export const URLS = {
  DSM: 'http://localhost:3001',
  PROPOSALS: 'http://localhost:3002',
  OTRS: 'http://localhost:3003',
  REPORTS: 'http://localhost:3004'
};

export const getHeader = jest.fn(() => ({
  'Authorization': 'Bearer mock-token'
}));
