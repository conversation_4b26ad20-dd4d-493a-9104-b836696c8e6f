import { useState, useEffect } from 'react'
import { shallowEqual, useSelector } from "react-redux";
import { Col, Typography } from "antd";

import { Select } from "../../../../components/Select";

import { setConsumptionState } from "../../../../store/actions/consumption-hours-action";
import { dynamoGet } from "../../../../service/apiDsmDynamo";

const STATE = process.env.REACT_APP_STAGE

export const ContractTypeField = () => {
    const selectedContract = useSelector(state => state.consumption.selectedContract, shallowEqual)
    const contractTypes = useSelector(state => state.consumption.contractTypes, shallowEqual)

    const [loading, setLoading] = useState(false)

    useEffect(() => {
        getTypes();
    }, []);

    async function getTypes() {
        try {
            setLoading(true);

            let response = await dynamoGet(`${STATE}-crm-custom-fields`);
            let labels = [];

            if (response.length > 0) {
                const consumeTypes = response[0].deal.find(
                    (deal) => deal.name === "Tipo de Consumo"
                );
                labels = consumeTypes.options.map((option, index) => {
                    return {
                        value: index + 1,
                        label: option.label
                    }
                });
            }

            labels.sort((a, b) => a.label.localeCompare(b.label))
            labels.unshift({ value: 0, label: "Todos" })

            setConsumptionState({
                field: 'contractTypes',
                value: labels
            })

            setLoading(false);
        } catch (error) {
            console.log(error)
            setLoading(false);
        }
    }

    return (
        <Col
            xl={{ span: 4 }}
            lg={{ span: 10 }}
            md={{ span: 6 }}
            sm={{ span: 10 }}
            style={{ marginBottom: 10 }}
        >
            <Typography.Text>Tipos de Contrato</Typography.Text>
            <Select
                onChange={(value) => setConsumptionState({ field: 'selectedContract', value })}
                options={contractTypes}
                value={selectedContract}
                style={{ width: "100%" }}
                loading={loading}
            />
        </Col>
    )
}