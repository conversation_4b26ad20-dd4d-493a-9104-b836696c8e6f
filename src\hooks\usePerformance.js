import { useState, useEffect, useCallback, useRef } from 'react';
import { performanceService } from '../services/performanceService';

/**
 * Hook para monitoramento de performance
 * Fornece métricas e ferramentas de medição
 */
export const usePerformance = (options = {}) => {
  const {
    autoUpdate = true,
    updateInterval = 5000,
    trackCustomMetrics = true
  } = options;

  const [metrics, setMetrics] = useState({});
  const [coreWebVitals, setCoreWebVitals] = useState({});
  const [resourceStats, setResourceStats] = useState({});
  const [isSupported, setIsSupported] = useState(false);
  
  const intervalRef = useRef(null);

  /**
   * Atualiza métricas
   */
  const updateMetrics = useCallback(() => {
    if (!performanceService.isSupported) return;

    const allMetrics = performanceService.getAllMetrics();
    const vitals = performanceService.getCoreWebVitals();
    const resources = performanceService.getResourceStats();

    setMetrics(allMetrics);
    setCoreWebVitals(vitals);
    setResourceStats(resources);
  }, []);

  /**
   * Marca início de operação
   */
  const markStart = useCallback((name) => {
    return performanceService.markStart(name);
  }, []);

  /**
   * Marca fim de operação
   */
  const markEnd = useCallback((name) => {
    return performanceService.markEnd(name);
  }, []);

  /**
   * Mede duração de função
   */
  const measureFunction = useCallback((name, fn) => {
    return performanceService.measureFunction(name, fn);
  }, []);

  /**
   * Gera relatório completo
   */
  const generateReport = useCallback(() => {
    return performanceService.generateReport();
  }, []);

  /**
   * Obtém uso de memória
   */
  const getMemoryUsage = useCallback(() => {
    return performanceService.getMemoryUsage();
  }, []);

  // Configurar atualização automática
  useEffect(() => {
    setIsSupported(performanceService.isSupported);
    
    if (autoUpdate && performanceService.isSupported) {
      updateMetrics();
      
      intervalRef.current = setInterval(updateMetrics, updateInterval);
      
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [autoUpdate, updateInterval, updateMetrics]);

  return {
    // Estado
    metrics,
    coreWebVitals,
    resourceStats,
    isSupported,
    
    // Ações
    markStart,
    markEnd,
    measureFunction,
    updateMetrics,
    generateReport,
    getMemoryUsage
  };
};

/**
 * Hook para medir performance de componentes
 */
export const useComponentPerformance = (componentName) => {
  const mountTimeRef = useRef(null);
  const [renderCount, setRenderCount] = useState(0);
  const [averageRenderTime, setAverageRenderTime] = useState(0);
  const renderTimesRef = useRef([]);

  // Marcar início do mount
  useEffect(() => {
    mountTimeRef.current = performance.now();
    performanceService.markStart(`component-${componentName}-mount`);
    
    return () => {
      // Marcar fim do unmount
      performanceService.markEnd(`component-${componentName}-mount`);
    };
  }, [componentName]);

  // Contar renders e medir tempo
  useEffect(() => {
    const renderStart = performance.now();
    
    setRenderCount(prev => prev + 1);
    
    // Medir tempo de render (aproximado)
    const renderEnd = performance.now();
    const renderTime = renderEnd - renderStart;
    
    renderTimesRef.current.push(renderTime);
    
    // Calcular média (últimos 10 renders)
    const recentTimes = renderTimesRef.current.slice(-10);
    const average = recentTimes.reduce((sum, time) => sum + time, 0) / recentTimes.length;
    setAverageRenderTime(average);
  });

  const getMountTime = useCallback(() => {
    if (mountTimeRef.current) {
      return performance.now() - mountTimeRef.current;
    }
    return null;
  }, []);

  return {
    renderCount,
    averageRenderTime,
    getMountTime,
    componentName
  };
};

/**
 * Hook para medir performance de APIs
 */
export const useAPIPerformance = () => {
  const [apiMetrics, setApiMetrics] = useState({});

  const measureAPI = useCallback(async (name, apiCall) => {
    const startTime = performance.now();
    
    try {
      const result = await apiCall();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setApiMetrics(prev => ({
        ...prev,
        [name]: {
          ...prev[name],
          lastCall: duration,
          totalCalls: (prev[name]?.totalCalls || 0) + 1,
          totalTime: (prev[name]?.totalTime || 0) + duration,
          averageTime: ((prev[name]?.totalTime || 0) + duration) / ((prev[name]?.totalCalls || 0) + 1),
          successCount: (prev[name]?.successCount || 0) + 1
        }
      }));
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setApiMetrics(prev => ({
        ...prev,
        [name]: {
          ...prev[name],
          lastCall: duration,
          totalCalls: (prev[name]?.totalCalls || 0) + 1,
          totalTime: (prev[name]?.totalTime || 0) + duration,
          averageTime: ((prev[name]?.totalTime || 0) + duration) / ((prev[name]?.totalCalls || 0) + 1),
          errorCount: (prev[name]?.errorCount || 0) + 1
        }
      }));
      
      throw error;
    }
  }, []);

  const getAPIStats = useCallback((name) => {
    return apiMetrics[name] || null;
  }, [apiMetrics]);

  const getAllAPIStats = useCallback(() => {
    return apiMetrics;
  }, [apiMetrics]);

  return {
    measureAPI,
    getAPIStats,
    getAllAPIStats,
    apiMetrics
  };
};

/**
 * Hook para monitorar Core Web Vitals em tempo real
 */
export const useCoreWebVitals = () => {
  const [vitals, setVitals] = useState({
    LCP: null,
    FID: null,
    CLS: null,
    FCP: null,
    TTFB: null
  });
  
  const [scores, setScores] = useState({
    LCP: 'unknown',
    FID: 'unknown',
    CLS: 'unknown',
    FCP: 'unknown',
    TTFB: 'unknown'
  });

  /**
   * Calcula score baseado nos thresholds do Google
   */
  const calculateScore = useCallback((metric, value) => {
    if (value === null || value === undefined) return 'unknown';
    
    const thresholds = {
      LCP: { good: 2500, poor: 4000 },
      FID: { good: 100, poor: 300 },
      CLS: { good: 0.1, poor: 0.25 },
      FCP: { good: 1800, poor: 3000 },
      TTFB: { good: 600, poor: 1500 }
    };
    
    const threshold = thresholds[metric];
    if (!threshold) return 'unknown';
    
    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }, []);

  /**
   * Atualiza vitals e scores
   */
  const updateVitals = useCallback(() => {
    const newVitals = performanceService.getCoreWebVitals();
    setVitals(newVitals);
    
    const newScores = {};
    Object.keys(newVitals).forEach(metric => {
      newScores[metric] = calculateScore(metric, newVitals[metric]);
    });
    setScores(newScores);
  }, [calculateScore]);

  // Atualizar vitals periodicamente
  useEffect(() => {
    if (!performanceService.isSupported) return;
    
    updateVitals();
    
    const interval = setInterval(updateVitals, 2000);
    
    return () => clearInterval(interval);
  }, [updateVitals]);

  /**
   * Obtém score geral
   */
  const getOverallScore = useCallback(() => {
    const scoreValues = { good: 3, 'needs-improvement': 2, poor: 1, unknown: 0 };
    const validScores = Object.values(scores).filter(score => score !== 'unknown');
    
    if (validScores.length === 0) return 'unknown';
    
    const average = validScores.reduce((sum, score) => sum + scoreValues[score], 0) / validScores.length;
    
    if (average >= 2.5) return 'good';
    if (average >= 1.5) return 'needs-improvement';
    return 'poor';
  }, [scores]);

  return {
    vitals,
    scores,
    updateVitals,
    getOverallScore,
    isSupported: performanceService.isSupported
  };
};

/**
 * Hook para detectar performance issues
 */
export const usePerformanceIssues = () => {
  const [issues, setIssues] = useState([]);
  const { coreWebVitals, resourceStats } = usePerformance();

  useEffect(() => {
    const newIssues = [];
    
    // Verificar Core Web Vitals
    if (coreWebVitals.LCP > 4000) {
      newIssues.push({
        type: 'LCP',
        severity: 'high',
        message: 'Largest Contentful Paint is too slow',
        value: coreWebVitals.LCP,
        threshold: 2500
      });
    }
    
    if (coreWebVitals.FID > 300) {
      newIssues.push({
        type: 'FID',
        severity: 'high',
        message: 'First Input Delay is too high',
        value: coreWebVitals.FID,
        threshold: 100
      });
    }
    
    if (coreWebVitals.CLS > 0.25) {
      newIssues.push({
        type: 'CLS',
        severity: 'medium',
        message: 'Cumulative Layout Shift is too high',
        value: coreWebVitals.CLS,
        threshold: 0.1
      });
    }
    
    // Verificar recursos
    if (resourceStats.totalSize > 5000000) { // 5MB
      newIssues.push({
        type: 'BUNDLE_SIZE',
        severity: 'medium',
        message: 'Total bundle size is too large',
        value: resourceStats.totalSize,
        threshold: 2000000
      });
    }
    
    if (resourceStats.slowest && resourceStats.slowest.duration > 5000) {
      newIssues.push({
        type: 'SLOW_RESOURCE',
        severity: 'medium',
        message: `Slow resource: ${resourceStats.slowest.name}`,
        value: resourceStats.slowest.duration,
        threshold: 2000
      });
    }
    
    setIssues(newIssues);
  }, [coreWebVitals, resourceStats]);

  return {
    issues,
    hasIssues: issues.length > 0,
    criticalIssues: issues.filter(issue => issue.severity === 'high'),
    warningIssues: issues.filter(issue => issue.severity === 'medium')
  };
};

export default usePerformance;
