import React, {useState, useEffect} from 'react'
import {ArrowLeftOutlined} from '@ant-design/icons'
import '../ContestHours/contest.css'
import 'antd/dist/antd.css'

const ContestHours = ({selectedTickets, handleContestTicketView}) => {
    const [messageSend, setMessageSend] = useState(false)

    useEffect(() => {

    }, [messageSend])

    function sendContestation() {
        setMessageSend(true)
    }

    return (
        <>
            <div className="table-card-top contest-header">
                <button className="filter-clear-btn back-button" onClick={() => handleContestTicketView()}>
                    <ArrowLeftOutlined/>
                </button>
                <h2 className="contestHours-title">Contestação de horas</h2>
            </div>
            <div className="first-box">
                <div className='contest-card'>
                    {selectedTickets.map((ticket) => <p key={ticket.key}>{ticket.title}</p>)}
                </div>
            </div>

            <div className='text-card'>
                <textarea name="contest" id="contest" cols="30" rows="10" placeholder='Escreva seu texto'
                          required></textarea>
            </div>
            {!messageSend ? (
                <div className="button-send" onClick={sendContestation}>
                    <button className="button-inside">
                        Enviar
                    </button>
                </div>
            ) : (
                <div className="constest-sent">
                    <p>Contestação enviada!!!</p>
                    <div className="button-send" onClick={sendContestation}>
                        <button className="button-inside">
                            Enviar
                        </button>
                    </div>
                </div>
            )}
        </>
    )
}

export {ContestHours}
