import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  But<PERSON>,
  Tag,
  List,
  Tabs,
  Typography,
  Space,
  Col,
  Row,
  Select,
  Empty,
  Spin,
} from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, Tooltip, LabelList } from "recharts";
import { dynamoGetById } from "../../../service/apiDsmDynamo";
import moment from "moment";
import * as controller from "./controllers/visualizeInfo";
import { formattedCommitments } from "./controllers/editBilling";

export const VisualizeInfo = ({ item, permissions }) => {
  const [showModal, setShowModal] = useState(false);
  const [activeTab, setActiveTab] = useState("1");
  const billing = item.payment_percent[item?.contractIndex];
  const [contract, setContract] = useState();

  const getContract = async () => {
    try {
      const data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-contracts`,
        billing.contract_id
      );
      setContract(data);
    } catch (err) {
      console.log(err);
    }
  };

  const genereteLink = (baseURL) => {
    let customerName = item?.names?.name || item?.names?.fantasy_name;
    customerName = customerName.replace(/[<>:"/\\|?*.]/g, "");
    customerName = encodeURIComponent(customerName);
    const formattedValuetedURL = `${baseURL}%2F${customerName}`;
    return formattedValuetedURL;
  };

  const items = [
    {
      key: "1",
      label: "Informações gerais",
      permissionCode: "view_info_modal_tab_general_info",
      children: [
        { key: "taxes", title: "Impostos:", value: billing?.taxes },
        {
          key: "led_support",
          title: "Led Support:",
          value: billing?.led_support,
        },
      ],
    },
    {
      key: "2",
      label: "Documentos úteis",
      permissionCode: "view_info_modal_tab_useful_links",
      children: [
        {
          key: "contract_link",
          title: "Link do contrato:",
          value: (
            <a href={genereteLink(process.env.REACT_APP_BILLING_CONTRACT_LINK)}>
              Contrato assinado
            </a>
          ),
        },
        {
          key: "billing_link",
          title: "Link do faturamento:",

          value: (
            <a href={genereteLink(process.env.REACT_APP_BILLING_LINK)}>
              Faturamento do cliente
            </a>
          ),
        },
      ],
    },
  ];

  if (contract?.discount === "edp") {
    formattedCommitments(contract.edp_commitment);
    items.push({
      key: "3",
      label: "EDP commitment",
      permissionCode: "view_info_modal_tab_edp_commitment",
      children: [
        {
          key: "graph",
          value: <ViewGraph customer={item} contract={contract} />,
        },
      ],
    });
  }

  return (
    <>
      <Button
        style={{ padding: "0" }}
        type="text"
        onClick={async () => {
          setShowModal(true);
          await getContract();
        }}
      >
        <Tag color="#0f9347">Visualizar</Tag>
      </Button>
      <Modal
        title="Informações de Billing"
        open={showModal}
        width={activeTab === "3" ? 1100 : 500}
        onCancel={() => setShowModal(false)}
        footer={[
          <Button key={"closeButton"} onClick={() => setShowModal(false)}>
            Fechar
          </Button>,
        ]}
      >
        {contract ? (
          <Tabs
            type="card"
            defaultActiveKey="1"
            onChange={(key) => setActiveTab(key)}
            items={items.map((item) => ({
              disabled: !permissions.data.some(
                (permission) => permission.code === item.permissionCode
              ),
              key: item.key,
              label: item.label,
              children: (
                <List>
                  {item.children.map((child) => (
                    <List.Item key={child.key}>
                      <List.Item.Meta
                        title={child.title}
                        description={child.value}
                      />
                    </List.Item>
                  ))}
                </List>
              ),
            }))}
          />
        ) : (
          <Spin tip="Carregando...">
            <div className="content" />
          </Spin>
        )}
      </Modal>
    </>
  );
};

const ViewGraph = (props) => {
  const { Text } = Typography;
  const { Option } = Select;
  const { customer, contract } = props;
  const [invoices, setInvoices] = useState([]);
  const [selectedRange, setSelectedRange] = useState(
    contract.edp_commitment[0].commitmentRange
  );
  const [commitmentValue, setCommitmentValue] = useState(
    contract.edp_commitment[0].commitmentValue
  );
  const [loading, setLoading] = useState(false);
  const [contractPercentage, setContractPercentage] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const index = contract.edp_commitment.findIndex(
        (item) =>
          item.commitmentRange.startDate === selectedRange.startDate &&
          item.commitmentRange.endDate === selectedRange.endDate
      );

      if (index !== -1) {
        setCommitmentValue(contract.edp_commitment[index].commitmentValue);
      }

      const invoiceList = await controller.getPayerInvoices(
        customer,
        selectedRange
      );
      setInvoices(invoiceList.filter((invoice) => invoice !== null));
      const invoicesPromises = contract.edp_commitment.map(
        async (commitment) => {
          const invoices = await controller.getPayerInvoices(
            customer,
            commitment.commitmentRange
          );

          return invoices.filter((invoice) => invoice !== null);
        }
      );
      const allInvoices = (await Promise.all(invoicesPromises)).flat();
      const calculateContractPercentage =
        await controller.calculateContractPercentage(contract, allInvoices);
      setContractPercentage(calculateContractPercentage);

      setLoading(false);
    };

    fetchData();
  }, [customer, selectedRange, contract.edp_commitment]);

  return (
    <>
      <Row>
        <Col>
          <Space size="middle" style={{ marginLeft: 30 }}>
            <Text>Período: </Text>
            <Select
              onChange={(value) => {
                const [startDate, endDate] = value.split(" - ");
                setSelectedRange({ startDate, endDate });
                setLoading(true);
              }}
              defaultValue={`${selectedRange.startDate} - ${selectedRange.endDate}`}
            >
              {contract?.edp_commitment?.map((item) => {
                return (
                  <Option
                    key={item.id}
                    value={`${item.commitmentRange.startDate} - ${item.commitmentRange.endDate}`}
                  >
                    {`${moment(item.commitmentRange.startDate).format(
                      "MM/YYYY"
                    )} - ${moment(item.commitmentRange.endDate).format(
                      "MM/YYYY"
                    )}`}
                  </Option>
                );
              })}
            </Select>
          </Space>
        </Col>
      </Row>
      {invoices.length > 0 ? (
        <>
          <Row justify={"space-between"} align={"middle"}>
            <Col>
              <BarChart
                width={800}
                height={300}
                data={invoices}
                margin={{
                  top: 40,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <XAxis
                  dataKey="name"
                  angle={-25}
                  textAnchor="end"
                  height={50}
                />
                <Tooltip />
                <Bar dataKey="$" stackId="a" fill="#8884d8">
                  <LabelList
                    dataKey="formattedValue"
                    position="top"
                    fontSize={10}
                    fontWeight={"bold"}
                  />
                </Bar>
              </BarChart>
              <Row align="middle" justify={"center"}>
                <Text style={{ color: "#CCCCCC", marginTop: "12px" }}>
                  *Os valores apresentados são aproximados. Para obter valores
                  precisos, consulte a AWS.
                </Text>
              </Row>
            </Col>
            <Col>
              <Row>
                <Text>Valor do commitment: ${commitmentValue} </Text>
              </Row>
              <Row>
                <Text>
                  Total do período: {controller.calculateTotal(invoices)}{" "}
                </Text>
              </Row>
              <Row>
                <Text>
                  % do período:{" "}
                  {controller.calculatePercentage(invoices, commitmentValue)} %{" "}
                </Text>
              </Row>
              <Row>
                <Text>
                  % do contrato:{" "}
                  {contractPercentage !== "" ? (
                    `${contractPercentage}  %`
                  ) : (
                    <LoadingOutlined spin />
                  )}
                </Text>
              </Row>
            </Col>
          </Row>
        </>
      ) : loading ? (
        <Row justify="center" align="middle">
          <Spin />
        </Row>
      ) : (
        <Empty description={"Sem dados para esse período"} />
      )}
    </>
  );
};
