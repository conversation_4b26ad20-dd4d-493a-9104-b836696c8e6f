{"rules": {"react/jsx-uses-react": "error", "react/jsx-uses-vars": "error", "react/no-unused-state": "error", "react/no-direct-mutation-state": "error", "react/no-deprecated": "error", "react/no-string-refs": "error", "react/require-render-return": "error", "react/jsx-no-duplicate-props": "error", "react/jsx-no-undef": "error", "react/jsx-pascal-case": "error", "react/no-danger-with-children": "error", "react/no-unknown-property": "error", "react/prop-types": "off", "react/display-name": "off", "no-unused-vars": "warn", "no-console": "warn", "no-debugger": "error", "no-alert": "warn", "no-eval": "error", "no-implied-eval": "error", "no-new-func": "error", "no-script-url": "error", "no-self-assign": "error", "no-self-compare": "error", "no-sequences": "error", "no-throw-literal": "error", "no-unmodified-loop-condition": "error", "no-unused-expressions": "error", "no-useless-call": "error", "no-useless-concat": "error", "no-useless-escape": "error", "no-useless-return": "error", "no-void": "error", "no-with": "error", "prefer-promise-reject-errors": "error", "radix": "error", "require-await": "error", "vars-on-top": "error", "wrap-iife": "error", "yoda": "error", "no-delete-var": "error", "no-label-var": "error", "no-restricted-globals": "error", "no-shadow": "error", "no-shadow-restricted-names": "error", "no-undef": "error", "no-undef-init": "error", "no-undefined": "off", "no-use-before-define": "error", "array-bracket-newline": "off", "array-bracket-spacing": ["error", "never"], "array-element-newline": "off", "block-spacing": ["error", "always"], "brace-style": ["error", "1tbs", {"allowSingleLine": true}], "camelcase": ["error", {"properties": "never"}], "capitalized-comments": "off", "comma-dangle": ["error", "never"], "comma-spacing": ["error", {"before": false, "after": true}], "comma-style": ["error", "last"], "computed-property-spacing": ["error", "never"], "consistent-this": "off", "eol-last": ["error", "always"], "func-call-spacing": ["error", "never"], "func-name-matching": "off", "func-names": "off", "func-style": "off", "function-paren-newline": "off", "id-blacklist": "off", "id-length": "off", "id-match": "off", "implicit-arrow-linebreak": "off", "indent": ["error", 2, {"SwitchCase": 1}], "jsx-quotes": ["error", "prefer-double"], "key-spacing": ["error", {"beforeColon": false, "afterColon": true}], "keyword-spacing": ["error", {"before": true, "after": true}], "line-comment-position": "off", "linebreak-style": "off", "lines-around-comment": "off", "lines-between-class-members": ["error", "always"], "max-depth": "off", "max-len": "off", "max-lines": "off", "max-lines-per-function": "off", "max-nested-callbacks": "off", "max-params": "off", "max-statements": "off", "max-statements-per-line": "off", "multiline-comment-style": "off", "multiline-ternary": "off", "new-cap": ["error", {"newIsCap": true, "capIsNew": false}], "new-parens": "error", "newline-per-chained-call": "off", "no-array-constructor": "error", "no-bitwise": "off", "no-continue": "off", "no-inline-comments": "off", "no-lonely-if": "error", "no-mixed-operators": "off", "no-mixed-spaces-and-tabs": "error", "no-multi-assign": "off", "no-multiple-empty-lines": ["error", {"max": 2, "maxEOF": 1}], "no-negated-condition": "off", "no-nested-ternary": "off", "no-new-object": "error", "no-plusplus": "off", "no-restricted-syntax": "off", "no-tabs": "error", "no-ternary": "off", "no-trailing-spaces": "error", "no-underscore-dangle": "off", "no-unneeded-ternary": ["error", {"defaultAssignment": false}], "no-whitespace-before-property": "error", "nonblock-statement-body-position": "off", "object-curly-newline": "off", "object-curly-spacing": ["error", "always"], "object-property-newline": "off", "one-var": ["error", "never"], "one-var-declaration-per-line": "off", "operator-assignment": "off", "operator-linebreak": "off", "padded-blocks": ["error", "never"], "padding-line-between-statements": "off", "prefer-object-spread": "error", "quote-props": ["error", "as-needed"], "quotes": ["error", "single", {"avoidEscape": true}], "require-jsdoc": "off", "semi": ["error", "always"], "semi-spacing": ["error", {"before": false, "after": true}], "semi-style": ["error", "last"], "sort-keys": "off", "sort-vars": "off", "space-before-blocks": ["error", "always"], "space-before-function-paren": ["error", {"anonymous": "always", "named": "never", "asyncArrow": "always"}], "space-in-parens": ["error", "never"], "space-infix-ops": "error", "space-unary-ops": ["error", {"words": true, "nonwords": false}], "spaced-comment": ["error", "always"], "switch-colon-spacing": ["error", {"after": true, "before": false}], "template-tag-spacing": ["error", "never"], "unicode-bom": ["error", "never"], "wrap-regex": "off"}, "env": {"browser": true, "es6": true, "node": true, "jest": true}, "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "settings": {"react": {"version": "detect"}}}