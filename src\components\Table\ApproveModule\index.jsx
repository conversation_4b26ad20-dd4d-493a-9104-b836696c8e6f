import React, { useState, useEffect } from 'react'
import { ProfessionalTickets } from '../ProfessionalTickets'
import { Table, Select, Typography, Row, Col, Card} from 'antd'
import 'antd/dist/antd.css'

const columns = [
  {
    title: 'Profissional',
    dataIndex: 'professional',
    key: 'professional',
    render: (professional) => <p className="table-font">{professional}</p>,
  },
  {
    title: 'Agosto',
    dataIndex: 'august',
    key: 'august',
    render: (august) => <p className="table-font">{august}</p>,
  },
  {
    title: 'Julho',
    dataIndex: 'july',
    key: 'july',
    render: (july) => <p className="table-font">{july}</p>,
  },
  {
    title: 'Junho',
    dataIndex: 'june',
    key: 'june',
    render: (june) => <p className='table-font'>{june}</p>,
  },
]

const ApproveModule = (props) => {
  const {Title, Text} = Typography;
  const [tablePage, setTablePage] = useState(true)
  const [data, setData] = useState([])
  const [filteredProfessional, setFilteredProfessional] = useState([])
  const { Option } = Select;

  useEffect(() => {
    setData(
      [
        {
          key: '1',
          professional: 'José Silveira',
          august: '5h',
          july: '6h 30min',
          june: '9h',
        },
        {
          key: '2',
          professional: 'José',
          august: '4h',
          july: '3h 30min',
          june: '8h',
        },
        {
          key: '3',
          professional: 'Silveira',
          august: '3h',
          july: '3h 30min',
          june: '4h',
        },
        {
          key: '4',
          professional: 'Douglas',
          august: '2h',
          july: '2h 30min',
          june: '2h',
        },
        {
          key: '5',
          professional: 'Gilberto',
          august: '1h',
          july: '1h 30min',
          june: '5h',
        },
      ]  
    )
  }, [])

  useEffect(() => {
    setFilteredProfessional(data)
  }, [data])

  function showModal() {
    setTablePage(!tablePage)
  }

  // function getFilteredProfessional(name) {
  //   let filteredProfessional = []
  //   Object.entries(allProfessionals).forEach((professional) => {
  //     if(professional[1].name === name) {
  //       filteredProfessional.push(professional[1])
  //     }
  //   })
  // }

  function getFilteredProfessional(name) {
    if(name === "Todos"){
      return setFilteredProfessional(data)
    }
    let professionals = []
    data.forEach((professional) => {
      if(professional.professional === name) {
        professionals.push(professional)
      }
    })
    setFilteredProfessional(professionals)
  }

  return (
    <Card bordered={false} 
      style={{
          borderRadius: '20px', 
          height: '100%',
          boxShadow: "0 0 10px rgba(0,0,0,0.1)",
      }}
    >
      {tablePage === true ? (
        <Row justify='space-between' align='middle'>
          <Col>
            <Title level={3} style={{fontWeight: 400}}>Módulo para aprovação de horas</Title>
          </Col>
          <Col>
            <Select
              mode="default"
              placeholder="Todos os Profissionais"
              onChange={(value) => getFilteredProfessional(value)}
            >
              <Option value={'Todos'}>{'Todos'}</Option>
              {data.map((value) => (
                <Option value={value.professional}>{value.professional}</Option>
              ))}
            </Select>
          </Col>
        <Col span={24}>
          <Table 
            onRow={() => {
              return {
                onClick: event => {
                  showModal()
                }
              }
            }}
            scroll={{ x: '100%' }}
            pagination={false}
            columns={columns}
            dataSource={filteredProfessional}
          >
          </Table>
        </Col>
        </Row>
        ) : (
        <ProfessionalTickets showModal={showModal}/>
        )} 
    </Card> 
  )
}

export { ApproveModule }
