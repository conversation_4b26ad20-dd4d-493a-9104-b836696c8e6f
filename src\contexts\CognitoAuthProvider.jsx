/**
 * AuthProvider Completo com Cognito e Cookies HttpOnly
 * Gerenciamento avançado de autenticação para React 18
 */

import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { Auth } from 'aws-amplify';
import axios from 'axios';
import { logger } from '../utils/logger';
import { AUTH_STATUS, AUTH_ERRORS, AUTH_ROUTES } from '../constants/auth';
import { initializeAmplify, getOAuthLoginUrl, getOAuthLogoutUrl } from '../config/amplify';
import { httpOnlyAuthService } from '../services/httpOnlyAuthService';

/**
 * Contexto de autenticação
 */
const CognitoAuthContext = createContext(null);

/**
 * AuthProvider com integração completa Cognito + Cookies HttpOnly
 */
export const CognitoAuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [error, setError] = useState(null);
  const [httpOnlySupported, setHttpOnlySupported] = useState(false);
  const [authMethod, setAuthMethod] = useState(null); // 'cognito', 'httponly', 'manual'
  const [amplifyInitialized, setAmplifyInitialized] = useState(false);
  const [interceptorsConfigured, setInterceptorsConfigured] = useState(false);

  /**
   * Inicializar Amplify na montagem do componente
   */
  useEffect(() => {
    const initAmplify = async () => {
      try {
        const success = initializeAmplify();
        setAmplifyInitialized(success);
        
        if (success) {
          logger.info('Amplify inicializado com sucesso');
        } else {
          logger.error('Falha na inicialização do Amplify');
        }
      } catch (error) {
        logger.error('Erro na inicialização do Amplify', { error: error.message });
        setAmplifyInitialized(false);
      }
    };

    initAmplify();
  }, []);

  /**
   * Verificar suporte a cookies HttpOnly
   */
  const checkHttpOnlySupport = useCallback(async () => {
    try {
      const supported = await httpOnlyAuthService.checkHttpOnlySupport();
      setHttpOnlySupported(supported);
      
      logger.info('Verificação de suporte HttpOnly', { supported });
      return supported;
    } catch (error) {
      logger.warn('Erro na verificação de suporte HttpOnly', { error: error.message });
      setHttpOnlySupported(false);
      return false;
    }
  }, []);

  /**
   * Configurar interceptors do Axios
   */
  const configureAxiosInterceptors = useCallback(() => {
    if (interceptorsConfigured) return;

    const requestInterceptor = axios.interceptors.request.use(
      async (config) => {
        // Verificar se é API interna
        const isInternalAPI = config.url?.includes(process.env.REACT_APP_API_PERMISSION) ||
                             config.baseURL?.includes(process.env.REACT_APP_API_PERMISSION);

        if (isInternalAPI) {
          if (httpOnlySupported) {
            // Usar cookies HttpOnly
            config.withCredentials = true;
            logger.debug('Usando cookies HttpOnly para requisição');
          } else {
            // Fallback para token manual
            try {
              const session = await Auth.currentSession();
              const token = session.getIdToken().getJwtToken();
              config.headers.Authorization = `Bearer ${token}`;
              logger.debug('Usando token Cognito para requisição');
            } catch (error) {
              logger.debug('Erro ao obter token Cognito', { error: error.message });
            }
          }
        }

        return config;
      },
      (error) => {
        logger.error('Erro no interceptor de request', { error: error.message });
        return Promise.reject(error);
      }
    );

    const responseInterceptor = axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            if (httpOnlySupported) {
              // Tentar refresh via cookies HttpOnly
              await httpOnlyAuthService.refreshToken();
              logger.info('Token refreshed via HttpOnly');
            } else {
              // Tentar refresh via Cognito
              const session = await Auth.currentSession();
              const newToken = session.getIdToken().getJwtToken();
              originalRequest.headers.Authorization = `Bearer ${newToken}`;
              logger.info('Token refreshed via Cognito');
            }

            // Retry da requisição original
            return axios(originalRequest);
          } catch (refreshError) {
            logger.error('Erro no refresh de token', { error: refreshError.message });
            
            // Se refresh falhou, fazer logout
            await handleLogout();
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );

    setInterceptorsConfigured(true);
    logger.info('Interceptors do Axios configurados');

    // Cleanup function
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, [httpOnlySupported, interceptorsConfigured]);

  /**
   * Verificar autenticação atual
   */
  const checkCurrentAuth = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Verificar suporte HttpOnly primeiro
      const httpOnlySupport = await checkHttpOnlySupport();

      if (httpOnlySupport) {
        // Tentar autenticação via cookies HttpOnly
        try {
          const isAuth = await httpOnlyAuthService.isAuthenticated();
          if (isAuth) {
            const userData = await httpOnlyAuthService.getCurrentUser();
            setUser(userData);
            setIsAuthenticated(true);
            setAuthMethod('httponly');
            logger.info('Autenticado via cookies HttpOnly', { user: userData.email });
            return;
          }
        } catch (error) {
          logger.debug('Falha na autenticação HttpOnly', { error: error.message });
        }
      }

      // Fallback para Cognito
      try {
        const session = await Auth.currentSession();
        const cognitoUser = await Auth.currentAuthenticatedUser();
        
        const userData = {
          email: cognitoUser.attributes.email,
          name: cognitoUser.attributes.name || cognitoUser.username,
          username: cognitoUser.username,
          groups: cognitoUser.signInUserSession?.accessToken?.payload['cognito:groups'] || [],
          permissions: cognitoUser.signInUserSession?.accessToken?.payload['cognito:groups'] || []
        };

        setUser(userData);
        setIsAuthenticated(true);
        setAuthMethod('cognito');
        logger.info('Autenticado via Cognito', { user: userData.email });

        // Se HttpOnly é suportado, converter para cookies
        if (httpOnlySupport) {
          try {
            const token = session.getIdToken().getJwtToken();
            await httpOnlyAuthService.setTokenCookie(token);
            setAuthMethod('httponly');
            logger.info('Token convertido para cookies HttpOnly');
          } catch (error) {
            logger.warn('Falha na conversão para HttpOnly', { error: error.message });
          }
        }

      } catch (error) {
        logger.debug('Usuário não autenticado', { error: error.message });
        setIsAuthenticated(false);
        setUser(null);
        setAuthMethod(null);
      }

    } catch (error) {
      logger.error('Erro na verificação de autenticação', { error: error.message });
      setError(error.message);
      setIsAuthenticated(false);
      setUser(null);
      setAuthMethod(null);
    } finally {
      setIsLoading(false);
    }
  }, [checkHttpOnlySupport]);

  /**
   * Login com código OAuth
   */
  const loginWithCode = useCallback(async (code) => {
    try {
      setIsLoading(true);
      setError(null);

      logger.info('Iniciando login com código OAuth', { codeLength: code.length });

      if (httpOnlySupported) {
        // Usar serviço HttpOnly
        const result = await httpOnlyAuthService.authenticate(code);
        setUser(result.user);
        setIsAuthenticated(true);
        setAuthMethod('httponly');
        logger.info('Login via HttpOnly bem-sucedido', { user: result.user.email });
        return result;
      } else {
        // Fallback para processo manual com Cognito
        try {
          // Trocar código por token via API Cognito
          const cognitoResponse = await axios.post(
            process.env.REACT_APP_COGNITO_PARSE,
            { code },
            {
              headers: { 'Content-Type': 'application/json' },
              withCredentials: false,
              timeout: 30000
            }
          );

          if (!cognitoResponse.data?.data?.id_token) {
            throw new Error('Token não encontrado na resposta do Cognito');
          }

          const { id_token } = cognitoResponse.data.data;

          // Decodificar token para obter dados do usuário
          const payload = JSON.parse(atob(id_token.split('.')[1]));
          const userData = {
            email: payload.email?.replace(/^azuread_/, ''),
            name: payload.name || payload.email?.split('@')[0],
            username: payload.email?.split('@')[0],
            groups: payload['cognito:groups'] || [],
            permissions: payload['cognito:groups'] || []
          };

          // Armazenar token manualmente
          await httpOnlyAuthService.setTokenCookie(id_token);

          setUser(userData);
          setIsAuthenticated(true);
          setAuthMethod('manual');
          logger.info('Login manual bem-sucedido', { user: userData.email });

          return { user: userData, token: id_token };

        } catch (cognitoError) {
          logger.error('Erro na autenticação com Cognito', { error: cognitoError.message });
          throw new Error('Falha na autenticação. Tente novamente.');
        }
      }

    } catch (error) {
      logger.error('Erro no login', { error: error.message });
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [httpOnlySupported]);

  /**
   * Login direto (redirecionar para OAuth)
   */
  const redirectToLogin = useCallback(() => {
    try {
      const loginUrl = getOAuthLoginUrl();
      logger.info('Redirecionando para login OAuth', { url: loginUrl });
      window.location.href = loginUrl;
    } catch (error) {
      logger.error('Erro ao redirecionar para login', { error: error.message });
      setError('Erro ao iniciar processo de login');
    }
  }, []);

  /**
   * Refresh token
   */
  const refreshToken = useCallback(async () => {
    try {
      if (httpOnlySupported && authMethod === 'httponly') {
        await httpOnlyAuthService.refreshToken();
        logger.info('Token refreshed via HttpOnly');
        return true;
      } else if (authMethod === 'cognito') {
        const session = await Auth.currentSession();
        logger.info('Token refreshed via Cognito');
        return true;
      } else {
        // Para método manual, tentar refresh via HttpOnly service
        await httpOnlyAuthService.refreshToken();
        logger.info('Token refreshed via manual method');
        return true;
      }
    } catch (error) {
      logger.error('Erro no refresh de token', { error: error.message });
      return false;
    }
  }, [httpOnlySupported, authMethod]);

  /**
   * Verificar se usuário tem permissão
   */
  const hasPermission = useCallback((permission) => {
    if (!user || !permission) return false;

    return user.permissions?.includes(permission) ||
           user.groups?.includes(permission) ||
           false;
  }, [user]);

  /**
   * Verificar se usuário tem qualquer uma das permissões
   */
  const hasAnyPermission = useCallback((permissions = []) => {
    if (!user || !permissions.length) return false;

    return permissions.some(permission => hasPermission(permission));
  }, [user, hasPermission]);

  /**
   * Verificar se usuário tem todas as permissões
   */
  const hasAllPermissions = useCallback((permissions = []) => {
    if (!user || !permissions.length) return false;

    return permissions.every(permission => hasPermission(permission));
  }, [user, hasPermission]);

  /**
   * Logout
   */
  const handleLogout = useCallback(async () => {
    try {
      setIsLoading(true);
      logger.info('Iniciando logout');

      // Logout via HttpOnly se suportado
      if (httpOnlySupported && authMethod === 'httponly') {
        await httpOnlyAuthService.logout();
      }

      // Logout via Cognito se necessário
      if (authMethod === 'cognito') {
        try {
          await Auth.signOut();
        } catch (error) {
          logger.warn('Erro no logout do Cognito', { error: error.message });
        }
      }

      // Limpar estado local
      setIsAuthenticated(false);
      setUser(null);
      setAuthMethod(null);
      setError(null);

      // Limpar localStorage
      localStorage.clear();

      logger.info('Logout concluído');

    } catch (error) {
      logger.error('Erro no logout', { error: error.message });
      // Mesmo com erro, limpar estado local
      setIsAuthenticated(false);
      setUser(null);
      setAuthMethod(null);
    } finally {
      setIsLoading(false);
    }
  }, [httpOnlySupported, authMethod]);

  /**
   * Inicializar autenticação
   */
  useEffect(() => {
    if (amplifyInitialized) {
      checkCurrentAuth();
    }
  }, [amplifyInitialized, checkCurrentAuth]);

  /**
   * Configurar interceptors quando necessário
   */
  useEffect(() => {
    if (amplifyInitialized && !interceptorsConfigured) {
      const cleanup = configureAxiosInterceptors();
      return cleanup;
    }
  }, [amplifyInitialized, interceptorsConfigured, configureAxiosInterceptors]);

  // Valor do contexto memoizado
  const contextValue = useMemo(() => ({
    isAuthenticated,
    isLoading,
    user,
    error,

    httpOnlySupported,
    authMethod,
    amplifyInitialized,

    login: loginWithCode,
    logout: handleLogout,
    checkAuth: checkCurrentAuth,
    redirectToLogin,
    refreshToken,

    // Helpers de usuário
    userEmail: user?.email,
    userName: user?.name || user?.username,
    userInitials: user?.name ?
      user.name.split(' ').map(n => n[0]).join('').toUpperCase() :
      user?.email?.substring(0, 2).toUpperCase(),

    hasPermission,
    hasAnyPermission,
    hasAllPermissions,

    // Status helpers
    isUnauthenticated: !isAuthenticated && !isLoading,
    status: isLoading ? AUTH_STATUS.LOADING :
            isAuthenticated ? AUTH_STATUS.AUTHENTICATED :
            AUTH_STATUS.UNAUTHENTICATED,

    // URLs OAuth
    getLoginUrl: getOAuthLoginUrl,
    getLogoutUrl: getOAuthLogoutUrl,

    AUTH_STATUS,
    AUTH_ERRORS,
    AUTH_ROUTES
  }), [
    isAuthenticated,
    isLoading,
    user,
    error,
    httpOnlySupported,
    authMethod,
    amplifyInitialized,
    loginWithCode,
    handleLogout,
    checkCurrentAuth,
    redirectToLogin,
    refreshToken,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions
  ]);

  return (
    <CognitoAuthContext.Provider value={contextValue}>
      {children}
    </CognitoAuthContext.Provider>
  );
};

/**
 * Hook para usar o contexto de autenticação
 */
export const useCognitoAuth = () => {
  const context = useContext(CognitoAuthContext);
  
  if (!context) {
    throw new Error('useCognitoAuth deve ser usado dentro de um CognitoAuthProvider');
  }
  
  return context;
};

export default CognitoAuthContext;
