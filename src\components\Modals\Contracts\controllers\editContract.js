import { differenceInCalendarMonths, format, parse } from "date-fns";
import moment from "moment";
import { editContractsFormFields } from "../constants/editContractsFormFields";

export const formatTagetDate = (date) => {
  if (!date) throw new Error("Missing date parameter");
  date = date.split("/").reverse().join("-");
  date = moment(date).format("YYYY/MM/DD 00:00:00");
  return date;
};

export const formatDateForMaskedInputs = (date) => {
  if (!date) throw new Error("Missing date parameter");
  let formattedDate = date.split(" ").shift();
  formattedDate = moment(formattedDate, "YYYY/MM/DD").format("DD/MM/YYYY");
  return formattedDate;
};

export const formatFormValues = (contractInfo) => {
  if (!contractInfo) throw new Error("Missing contractInfo parameter");
  let formattedFormValues = editContractsFormFields.map((item) => {
    let tempFormObj = {};
    if (item.key === "expected_start_date") {
      tempFormObj = {
        ...tempFormObj,
        objParam: item.key,
        value: formatDateForMaskedInputs(
          contractInfo?.target_dates?.expected_start_date
        ),
      };
    } else if (item.key === "expected_close_date") {
      tempFormObj = {
        ...tempFormObj,
        objParam: item.key,
        value: formatDateForMaskedInputs(
          contractInfo?.target_dates?.expected_close_date
        ),
      };
    } else if (item.formIndex) {
      let indexStart = item.formIndex.split(" ").shift();
      let indexEnd = item.formIndex.split(" ").pop();
      tempFormObj = {
        ...tempFormObj,
        objParam: item.key,
        value: contractInfo[indexStart][indexEnd],
      };
    } else {
      tempFormObj = {
        ...tempFormObj,
        objParam: item.key,
        value: contractInfo[item.key],
      };
    }

    return tempFormObj;
  });

  let contractValue = contractInfo?.values?.value?.toString();
  if (
    contractValue?.split(" ").shift() &&
    contractValue?.split(" ").shift() !== contractValue
  ) {
    formattedFormValues = [
      ...formattedFormValues,
      {
        objParam: "value_addon",
        value: contractValue?.split(" ").shift(),
      },
    ];
    formattedFormValues.forEach((item) => {
      if (item.objParam === "value") {
        item.value = contractValue?.split(" ").pop();
      }
    });
  }

  return formattedFormValues;
};

export const diffMonths = (closeDate, startDate) => {
  const expectedCloseDate = parse(closeDate, "dd/MM/yyyy", new Date());
  const expectedStartDate = parse(startDate, "dd/MM/yyyy", new Date());

  const durationInMonths = differenceInCalendarMonths(
    expectedCloseDate,
    expectedStartDate
  );
  return durationInMonths;
};

export const formatEditContractDSM = (body) => {
  if (!body) throw new Error("Missing body parameter");
  let formattedBodyRequest = {};
  for (let key of Object.entries(body)) {
    let tempBodyRequest = {};
    editContractsFormFields.forEach((item) => {
      if (item.formIndex) {
        if (item.formIndex.includes("identifications")) {
          tempBodyRequest = {
            ...tempBodyRequest,
            identifications: {
              itsm_id: body.itsm_id,
              crm_id: body.crm_id,
            },
          };
        } else if (item.formIndex.includes("percentage")) {
          tempBodyRequest = {
            ...tempBodyRequest,
            percentage: {
              freezing: body.freezing,
              block: body.block,
            },
          };
        } else if (item.formIndex.includes("target_dates")) {
          tempBodyRequest = {
            ...tempBodyRequest,
            duration: diffMonths(
              body.expected_close_date,
              body.expected_start_date
            ),
            target_dates: {
              expected_start_date: formatTagetDate(body.expected_start_date),
              expected_close_date: formatTagetDate(body.expected_close_date),
            },
          };
        } else if (item.formIndex.includes("values")) {
          tempBodyRequest = {
            ...tempBodyRequest,
            values: {
              aws_value: body.aws_value,
              value: body.value,
            },
          };
        }
      } else if (key[0] === item.key) {
        tempBodyRequest = {
          ...tempBodyRequest,
          [item.key]: body[item.key],
          updated_at: new Date(),
        };
      }
    });
    formattedBodyRequest = { ...formattedBodyRequest, ...tempBodyRequest };
  }
  return formattedBodyRequest;
};

export const formatDateOTRS = (date) => {
  if (!date) throw new Error("Missing date parameter");
  date = date.split(" ").shift();
  let formattedDate = moment(date, "DD/MM/YYYY").format("YYYY-MM-DD");
  return new Date(formattedDate);
};

export const formatCreateContractOTRS = (body, contract, client) => {
  if (!body) throw new Error("Missing body parameter");
  if (!contract) throw new Error("Missing contract parameter");

  let formattedBodyRequest = {
    name: body.name,
    customer_id: contract.customer.itsm_id || client?.identifications.itsm_id,
    start_date: format(formatDateOTRS(body.expected_start_date), "yyyy-MM-dd"),
    end_date: format(formatDateOTRS(body.expected_close_date), "yyyy-MM-dd"),
    notify_email: body?.emails?.length ? body.emails.toString() : "",
    total_hours: parseInt(body.total_hours),
    type_hours: body.type_hours,
    document_id: body.squad,
    duration: differenceInCalendarMonths(
      formatDateOTRS(body.expected_close_date),
      formatDateOTRS(body.expected_start_date)
    ),
    sla_id: null,
    type_id: null,
    priority_id: null,
    notify_percentage: null,
    freeze_percentage: null,
  };

  return formattedBodyRequest;
};

export const formatEditContractOTRS = (body, contract, client) => {
  if (!body) throw new Error("Missing body parameter");
  if (!contract) throw new Error("Missing contract parameter");

  let formattedBodyRequest = {
    start_date: format(formatDateOTRS(body.expected_start_date), "yyyy-MM-dd"),
    end_date: format(formatDateOTRS(body.expected_close_date), "yyyy-MM-dd"),
    notify_email: body?.emails?.length ? body.emails.toString() : "",
    customer_id: contract.customer.itsm_id || client?.identifications.itsm_id,
    notify_percentage: body.freezing,
    freeze_percentage: body.block,
    total_hours: body.total_hours,
    type_hours: body.type_hours,
    document_id: body.squad,
    name: body.name,
    duration: differenceInCalendarMonths(
      formatDateOTRS(body.expected_close_date),
      formatDateOTRS(body.expected_start_date)
    ),
  };
  return formattedBodyRequest;
};

export const formatEditStateContract = (contractInfo) => {
  if (!contractInfo) throw new Error("Missing contractInfo parameter");
  let formattedBodyValues = formatFormValues(contractInfo);
  let formattedBodyRequest = formattedBodyValues.reduce(
      (acc, item) => {
        return {
          ...acc,
          [item.objParam]: item.value,
        };
      },
      {
        excess_cost:
          typeof contractInfo?.excess_cost !== "string"
            ? 0
            : contractInfo?.excess_cost,
        id: contractInfo?.id,
        expected_start_date: formatDateForMaskedInputs(
          contractInfo?.target_dates?.expected_start_date
        ),
        expected_close_date: formatDateForMaskedInputs(
          contractInfo?.target_dates?.expected_close_date
        ),
      }
    );
  return formattedBodyRequest;
};

export const formatFormValuesSetter = (contractInfo) => {
  if (!contractInfo) throw new Error("Missing contractInfo parameter");
  let formattedFormValues = formatFormValues(contractInfo);
  let formattedData = formattedFormValues.reduce(
      (acc, item) => {
        return {
          ...acc,
          [item.objParam]: item.value,
        };
      },
      {
        excess_cost:
          typeof contractInfo?.excess_cost !== "string"
            ? 0
            : contractInfo?.excess_cost,
        id: contractInfo?.id,
        expected_start_date: formatDateForMaskedInputs(
          contractInfo?.target_dates?.expected_start_date
        ),
        expected_close_date: formatDateForMaskedInputs(
          contractInfo?.target_dates?.expected_close_date
        ),
      }
    );

  return formattedData;
};
