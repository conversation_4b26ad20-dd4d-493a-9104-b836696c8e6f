import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import "./styles/antd-custom.css";
import App from "./App";
import reportWebVitals from "./reportWebVitals";
import { store, persistor } from "./store/store";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { initializeErrorSuppression } from "./utils/errorSuppression";
import "./utils/validateEnvironment"; 
import "./utils/errorHandler";

initializeErrorSuppression();

const container = document.getElementById("root");
const root = createRoot(container);

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <App />
      </PersistGate>
    </Provider>
  </React.StrictMode>
);

reportWebVitals();
