import { But<PERSON>, Image, Row } from "antd";
import { useEffect, useState } from "react";
import Modal from "antd/lib/modal/Modal";
import { secret } from "../../../service/mfa";
const qrcode = require("qrcode");

export const ReadMFA = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [qrCode, setQrCode] = useState("");

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  useEffect(() => {
    qrcode.toDataURL(secret.otpauth_url, function (err, data) {
      setQrCode(data);
    });
  }, []);

  return (
    <>
      <Button type="link" onClick={showModal}>
        Cadastre seu Código MFA
      </Button>
      <Modal
        title="Leia o QR-Code"
        open={isModalVisible}
        onCancel={handleCancel}
        onOk={handleOk}
        okText="Fechar"
        cancelText="Cancelar"
      >
        Para realizar o cadastro do MFA, realize o seguinte passo-a-passo:{" "}
        <br />
        <br /> 1) Baixe o aplicativo Google Authenticator em seu smartphone;{" "}
        <br />
        <br /> 2) Clique no botão de adicionar, para ler o QR-Code;
        <br />
        <br /> 3) Digite o código apresentado.
        <br />
        <br />
        <Row align="center">
          <Image preview={false} src={qrCode} />
        </Row>
      </Modal>
    </>
  );
};
