import axios from 'axios';
import { verifyExpTime } from '../service/verifyExpTime';
import { logger } from '../utils/logger';
import { getApiUrl, getCognitoUrl, logDevInfo } from '../utils/devConfig';

/**
 * AuthService - Gerencia autenticação com cookies HttpOnly
 * Implementa melhores práticas de segurança para armazenamento de tokens
 */
class AuthService {
  constructor() {
    // Usar configuração de desenvolvimento que contorna problemas de CORS
    this.baseURL = getApiUrl();
    this.cognitoURL = getCognitoUrl();

    // Log da configuração de desenvolvimento
    logDevInfo();

    // REMOVIDO: setupAxiosInterceptors() - evita conflitos com httpOnlyAuthService
    // Os interceptors agora são gerenciados pelo httpOnlyAuthService

    // Inicialização: limpar tokens inseguros do localStorage
    this.initSecurityMigration();
  }

  /**
   * Inicializa migração de segurança removendo tokens do localStorage
   */
  initSecurityMigration() {
    // Verificar se há tokens no localStorage e alertar
    const hasLegacyTokens = localStorage.getItem('jwt') || localStorage.getItem('@dsm/token');

    if (hasLegacyTokens) {
      console.warn('🔒 MIGRAÇÃO DE SEGURANÇA: Tokens encontrados no localStorage serão migrados para cookies HttpOnly');

      // Agendar limpeza após um tempo para permitir migração
      setTimeout(() => {
        this.clearAllTokens();
      }, 5000); // 5 segundos para permitir migração automática
    }
  }

  /**
   * REMOVIDO: setupAxiosInterceptors()
   * Os interceptors globais foram removidos para evitar conflitos.
   * Agora usamos instâncias específicas do axios ou o httpOnlyAuthService.
   */

  /**
   * Obtém token do cookie (fallback para localStorage durante migração)
   */
  getTokenFromCookie() {
    // Primeiro tenta obter do cookie
    const cookies = document.cookie.split(';');
    const tokenCookie = cookies.find(cookie =>
      cookie.trim().startsWith('dsm_token=')
    );

    if (tokenCookie) {
      const token = tokenCookie.split('=')[1];
      if (token && token !== 'undefined' && token !== 'null') {
        return token;
      }
    }

    // Fallback para localStorage durante migração (será removido gradualmente)
    const legacyToken = localStorage.getItem('jwt') || localStorage.getItem('@dsm/token');

    if (legacyToken) {
      console.warn('⚠️ Token encontrado no localStorage. Migre para cookies para maior segurança.');
      // Tentar migrar automaticamente para cookie
      this.setTokenCookie(legacyToken).catch(() => {
        console.warn('Falha na migração automática do token para cookie');
      });
    }

    return legacyToken;
  }

  /**
   * Obtém token (prioriza cookie, fallback localStorage)
   * @deprecated Use getTokenFromCookie() para migração ou getAuthHeaders() para requisições
   * TODO: Remove this method after migration is complete
   */
  getToken() {
    console.warn('⚠️ getToken() is deprecated. Use getTokenFromCookie() instead.');
    return this.getTokenFromCookie();
  }

  /**
   * Define token no cookie HttpOnly via endpoint do backend
   * Como o endpoint não existe ainda, vamos usar uma abordagem temporária
   */
  async setTokenCookie(token) {
    // Determinar se está usando proxy ou URL real
    const isDevelopment = process.env.NODE_ENV === 'development';
    const baseURL = isDevelopment ? '/api' :
                   (this.baseURL.endsWith('/') ? this.baseURL.slice(0, -1) : this.baseURL);

    try {
      await axios.post(`${baseURL}/auth/set-token`,
        { token },
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      // Remove token do localStorage após migrar para cookie
      localStorage.removeItem('jwt');
      localStorage.removeItem('@dsm/token');

      return true;
    } catch (error) {
      // Determinar tipo de erro
      const isNetworkError = error.message === 'Network Error';
      const isCorsError = error.message.includes('CORS') || isNetworkError;
      const isNotFound = error.response?.status === 404;
      const isMissingToken = error.response?.data?.message === 'Missing Authentication Token';

      const errorType = isMissingToken || isNotFound ? 'ENDPOINT_NOT_DEPLOYED' :
                       isCorsError ? 'CORS_ERROR' : 'UNKNOWN_ERROR';

      console.warn('🔧 Endpoint /auth/set-token não disponível. Usando fallback funcional.', {
        endpoint: `${baseURL}/auth/set-token`,
        errorType,
        error: error.message,
        status: error.response?.status,
        fallback: 'Cookie manual (funcional mas menos seguro)',
        note: errorType === 'ENDPOINT_NOT_DEPLOYED' ? 'Deploy necessário' : 'Verificar configuração'
      });

      // Fallback temporário: usar cookie manual (menos seguro, mas funcional)
      this.setManualCookie(token);

      // Remove token do localStorage
      localStorage.removeItem('jwt');
      localStorage.removeItem('@dsm/token');

      return true;
    }
  }

  /**
   * Define cookie manualmente (fallback temporário)
   * NOTA: Não é HttpOnly, mas é melhor que localStorage
   */
  setManualCookie(token) {
    const expires = new Date();
    expires.setTime(expires.getTime() + (8 * 60 * 60 * 1000)); // 8 horas

    // Configuração otimizada para evitar avisos de SameSite
    const isSecureContext = window.location.protocol === 'https:' || window.location.hostname === 'localhost';
    const cookieConfig = [
      `dsm_token=${token}`,
      `expires=${expires.toUTCString()}`,
      'path=/',
      isSecureContext ? 'secure' : '',
      'samesite=lax' // Mudança: Lax em vez de Strict para melhor compatibilidade
    ].filter(Boolean).join('; ');

    document.cookie = cookieConfig;
    console.log('🍪 Token definido em cookie manual (fallback funcional)', {
      cookieName: 'dsm_token',
      expires: expires.toUTCString(),
      secure: isSecureContext,
      sameSite: 'lax',
      note: 'Deploy /auth/set-token para cookies HttpOnly seguros'
    });
  }

  /**
   * Autentica usuário com código do Cognito
   */
  async authenticate(code) {
    try {
      if (!code) {
        throw new Error('Código de autenticação não fornecido');
      }

      if (!this.cognitoURL) {
        throw new Error('URL do Cognito não configurada. Verifique REACT_APP_COGNITO_PARSE');
      }

      // Usar a URL correta do Cognito Parse com configuração otimizada para CORS
      const response = await axios.post(
        this.cognitoURL,
        { code },
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            // Evitar usar Authorization header aqui pois não é necessário para autenticação inicial
          },
          // Configuração otimizada para CORS
          withCredentials: false,
          timeout: 30000 // 30 segundos de timeout
        }
      );

      // Validar se a resposta tem a estrutura esperada
      if (!response.data || !response.data.data) {
        console.error('Resposta da API inválida:', response.data);
        throw new Error('Resposta da API inválida');
      }

      // Verificar se há erro na resposta
      if (response.data.data.error) {
        console.error('Erro na autenticação:', response.data.data.error);

        // Tratar erros específicos
        if (response.data.data.error === 'invalid_grant') {
          throw new Error('Código de autenticação expirado ou inválido. Faça login novamente.');
        }

        throw new Error(`Erro de autenticação: ${response.data.data.error}`);
      }

      if (!response.data.data.id_token) {
        console.error('Token não encontrado na resposta:', response.data);
        throw new Error('Token de autenticação não encontrado na resposta da API');
      }

      const { id_token } = response.data.data;

      // Validar se o token é válido antes de tentar decodificar
      if (!id_token || typeof id_token !== 'string' || !id_token.includes('.')) {
        throw new Error('Token de autenticação inválido');
      }

      // Decodifica token para obter informações do usuário
      const payload = JSON.parse(atob(id_token.split('.')[1]));

      if (!payload.email) {
        throw new Error('Email não encontrado no token');
      }

      const { email } = payload;

      // Remove prefixo "azuread_" se existir
      const cleanEmail = email.replace(/^azuread_/, '');

      // Define token no cookie
      await this.setTokenCookie(id_token);

      // Armazena informações do usuário (não sensíveis)
      const userData = {
        email: cleanEmail,
        username: cleanEmail.split('@')[0],
        name: cleanEmail.split('@')[0]
      };

      this.setUserData(userData);

      return { token: id_token, user: userData };
    } catch (error) {
      console.error('Erro na autenticação:', error);
      throw error;
    }
  }

  /**
   * Define dados do usuário no localStorage (dados não sensíveis)
   */
  setUserData(userData) {
    localStorage.setItem('@dsm/mail', userData.email);
    localStorage.setItem('@dsm/name', userData.name);
    localStorage.setItem('@dsm/username', userData.username);
    localStorage.setItem('@dsm/time', new Date().toISOString());
  }

  /**
   * Obtém dados do usuário
   */
  getUserData() {
    return {
      email: localStorage.getItem('@dsm/mail'),
      name: localStorage.getItem('@dsm/name'),
      username: localStorage.getItem('@dsm/username'),
      permission: localStorage.getItem('@dsm/permission'),
      time: localStorage.getItem('@dsm/time')
    };
  }

  /**
   * Define permissão do usuário
   */
  setUserPermission(permission) {
    localStorage.setItem('@dsm/permission', permission);
  }

  /**
   * Verifica se usuário está autenticado
   */
  async isAuthenticated() {
    const token = this.getTokenFromCookie();
    
    if (!token) {
      return false;
    }

    try {
      const isExpired = await verifyExpTime(token);
      return !isExpired;
    } catch (error) {
      console.error('Erro ao verificar token:', error);
      return false;
    }
  }

  /**
   * Obtém token atual (para compatibilidade)
   */
  getToken() {
    return this.getTokenFromCookie();
  }

  /**
   * Faz logout do usuário
   */
  async logout() {
    try {
      // Remove cookie do token via endpoint do backend
      await axios.post(`${this.baseURL}/auth/logout`, {}, {
        withCredentials: true
      });
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }

    // Limpa dados locais (incluindo tokens JWT)
    this.clearAllTokens();
    localStorage.clear();

    // Limpa cookies manualmente como fallback com configuração otimizada
    const isSecureContext = window.location.protocol === 'https:' || window.location.hostname === 'localhost';
    const clearCookieConfig = [
      'dsm_token=',
      'expires=Thu, 01 Jan 1970 00:00:00 UTC',
      'path=/',
      isSecureContext ? 'secure' : '',
      'samesite=lax'
    ].filter(Boolean).join('; ');

    document.cookie = clearCookieConfig;
  }

  /**
   * Remove todos os tokens JWT do localStorage (migração de segurança)
   */
  clearAllTokens() {
    const tokenKeys = ['jwt', '@dsm/token', 'token', 'authToken', 'access_token'];
    tokenKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        console.warn(`🔒 Removendo token inseguro do localStorage: ${key}`);
        localStorage.removeItem(key);
      }
    });
  }

  /**
   * Obtém headers de autorização
   */
  getAuthHeaders() {
    const token = this.getTokenFromCookie();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  /**
   * Obtém headers de autorização com formato correto (com Bearer)
   * @deprecated Use getAuthHeaders() que inclui 'Bearer '
   */
  getLegacyAuthHeaders() {
    const token = this.getTokenFromCookie();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  /**
   * Cria instância do axios configurada para usar cookies automaticamente
   */
  createApiInstance(baseURL) {
    const instance = axios.create({
      baseURL,
      withCredentials: false // Evita problemas de CORS
    });

    // Interceptor para adicionar token automaticamente
    instance.interceptors.request.use((config) => {
      const token = this.getTokenFromCookie();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Interceptor para tratar erros de autenticação
    instance.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          await this.logout();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );

    return instance;
  }

  /**
   * Cria instância do axios com configuração de autenticação
   */
  createAuthenticatedAxios(baseURL, options = {}) {
    const {
      withCredentials = false, // Mudança: padrão false para evitar problemas de CORS
      useTokenFromCookie = true
    } = options;

    const instance = axios.create({
      baseURL,
      withCredentials
    });

    if (instance && instance.interceptors) {
      instance.interceptors.request.use((config) => {
        let token = null;

      if (useTokenFromCookie) {
        token = this.getTokenFromCookie();
      } else {
        // Usar token do localStorage como fallback
        token = this.getTokenFromCookie();
      }

      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
    }

    return instance;
  }

  /**
   * Cria instância do axios sem credentials para APIs externas
   */
  createSimpleAxios(baseURL) {
    return this.createAuthenticatedAxios(baseURL, {
      withCredentials: false,
      useTokenFromCookie: false
    });
  }

  /**
   * Cria instância do axios com credentials para endpoints que requerem cookies
   */
  createCredentialedAxios(baseURL) {
    return this.createAuthenticatedAxios(baseURL, {
      withCredentials: true,
      useTokenFromCookie: true
    });
  }
}

// Exporta instância singleton
export const authService = new AuthService();
export default authService;

