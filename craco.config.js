const { BundleAnalyzerPlugin } = require("webpack-bundle-analyzer");
const WebpackBar = require("webpackbar");
// const CracoLessPlugin = require("craco-less"); // Temporariamente desabilitado

process.env.BROWSER = "none";

module.exports = {
  webpack: {
    plugins: [
      new WebpackBar({ profile: true }),
      ...(process.env.NODE_ENV === "production"
        ? [new BundleAnalyzerPlugin({
            openAnalyzer: false,
            analyzerMode: 'static',
            reportFilename: 'bundle-report.html',
            generateStatsFile: true,
            statsFilename: 'bundle-stats.json',
            logLevel: 'warn'
          })]
        : []),
    ],
    configure: (webpackConfig) => {
      webpackConfig.ignoreWarnings = [
        {
          module: /node_modules\/antd/,
          message: /Failed to parse source map/,
        },
        {
          module: /sass-loader/,
          message: /Deprecation/,
        }
      ];

      // Atualizar configuração do sass-loader para usar a API moderna
      const sassRules = webpackConfig.module.rules.find(
        rule => rule.oneOf
      ).oneOf.filter(
        rule => rule.test && rule.test.toString().includes('scss|sass')
      );

      sassRules.forEach(rule => {
        const sassLoaderIndex = rule.use.findIndex(
          ({ loader }) => loader && loader.includes('sass-loader')
        );
        
        if (sassLoaderIndex !== -1) {
          rule.use[sassLoaderIndex] = {
            loader: require.resolve('sass-loader'),
            options: {
              implementation: require('sass'),
              sassOptions: {
                fiber: false,
              },
            },
          };
        }
      });

      return webpackConfig;
    },
  },
  devServer: (devServerConfig, { env, paths, proxy, allowedHost }) => {
    if (devServerConfig.onBeforeSetupMiddleware || devServerConfig.onAfterSetupMiddleware) {
      const beforeSetup = devServerConfig.onBeforeSetupMiddleware;
      const afterSetup = devServerConfig.onAfterSetupMiddleware;

      devServerConfig.setupMiddlewares = (middlewares, devServer) => {
        if (beforeSetup) {
          beforeSetup(devServer);
        }

        if (afterSetup) {
          afterSetup(devServer);
        }

        return middlewares;
      };

      delete devServerConfig.onBeforeSetupMiddleware;
      delete devServerConfig.onAfterSetupMiddleware;
    }

    // Configurar proxy para contornar problemas de CORS em desenvolvimento
    if (env === 'development') {
      devServerConfig.proxy = {
        '/api': {
          target: 'https://api.dsm.darede.com.br',
          changeOrigin: true,
          secure: true,
          pathRewrite: {
            '^/api': '/dev'
          },
          onProxyReq: (proxyReq, req, res) => {
            console.log('🔄 Proxy request:', req.method, req.url, '→', proxyReq.path);
          },
          onError: (err, req, res) => {
            console.error('❌ Proxy error:', err.message);
          }
        },
        '/cognito-api': {
          target: 'https://lukr7ocjz4.execute-api.us-east-1.amazonaws.com',
          changeOrigin: true,
          secure: true,
          pathRewrite: {
            '^/cognito-api': '/dev/local'
          },
          onProxyReq: (proxyReq, req, res) => {
            console.log('🔄 Cognito proxy request:', req.method, req.url, '→', proxyReq.path);
          },
          onError: (err, req, res) => {
            console.error('❌ Cognito proxy error:', err.message);
          }
        }
      };
    }

    return devServerConfig;
  },
  // plugins: [
  //   {
  //     plugin: CracoLessPlugin,
  //     options: {
  //       lessLoaderOptions: {
  //         lessOptions: {
  //           modifyVars: {
  //             '@primary-color': '#00B050',
  //             '@link-color': '#00B050',
  //             '@success-color': '#00B050',
  //             '@border-radius-base': '2px',
  //             '@text-color': '#404040',
  //             '@pagination-item-bg-active': '#00B050',
  //             '@pagination-item-link-bg': '#ffffff',
  //             '@pagination-item-input-bg': '#ffffff',
  //           },
  //           javascriptEnabled: true,
  //         },
  //       },
  //     },
  //   },
  // ],
};
