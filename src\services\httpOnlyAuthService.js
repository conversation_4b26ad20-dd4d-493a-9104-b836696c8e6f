/**
 * Serviço de autenticação com cookies HttpOnly
 * Integração completa com o novo backend refatorado
 */

import axios from 'axios';
import { logger } from '../utils/logger';
import { config } from '../utils/validateEnvironment';
import { 
  AUTH_TIMEOUTS, 
  COOKIE_CONFIG, 
  STORAGE_KEYS, 
  AUTH_ROUTES,
  AUTH_ERRORS,
  DEFAULT_VALUES
} from '../constants/auth';

class HttpOnlyAuthService {
  constructor() {
    // Garantir que a URL base não termine com barra para evitar barras duplas
    this.baseURL = config.apiUrl.replace(/\/$/, '');
    this.isHttpOnlySupported = false; // Será verificado dinamicamente

    // Controle de interceptors para evitar duplicação
    this.interceptorsConfigured = false;

    // Configurar axios para usar cookies automaticamente
    this.setupAxiosDefaults();

    // NÃO verificar suporte HttpOnly na inicialização para evitar erros de CORS
    // this.checkHttpOnlySupport(); // DESABILITADO TEMPORARIAMENTE

    logger.info('HttpOnlyAuthService inicializado como serviço principal de autenticação', {
      baseURL: this.baseURL,
      environment: config.stage,
      note: 'Interceptors globais configurados - authService.js desabilitado'
    });
  }

  /**
   * Verifica se o backend suporta cookies HttpOnly
   */
  async checkHttpOnlySupport() {
    try {
      const response = await axios.get(`${this.baseURL}/auth/verify`, {
        withCredentials: true,
        timeout: 5000
      });
      
      this.isHttpOnlySupported = true;
      logger.info('Backend suporta cookies HttpOnly', {
        status: response.status
      });
      
      return true;
    } catch (error) {
      this.isHttpOnlySupported = false;
      logger.warn('Backend não suporta cookies HttpOnly ainda, usando fallback', {
        error: error.message,
        status: error.response?.status
      });
      return false;
    }
  }

  /**
   * Configura axios para usar cookies automaticamente
   * ÚNICO serviço responsável pelos interceptors globais
   */
  setupAxiosDefaults() {
    if (this.interceptorsConfigured) {
      logger.debug('Interceptors já configurados, pulando configuração');
      return;
    }

    axios.defaults.timeout = AUTH_TIMEOUTS.REQUEST_TIMEOUT;

    axios.interceptors.request.use(
      (config) => {
        const isInternalAPI = config.url?.includes('api.dsm.darede.com.br') ||
                             config.baseURL?.includes('api.dsm.darede.com.br') ||
                             config.url?.includes(process.env.REACT_APP_API_PERMISSION) ||
                             config.url?.startsWith('/api'); // Proxy local temporário

        if (isInternalAPI) {
          const isUsingProxy = config.url?.startsWith('/api');

          if (isUsingProxy) {
            // Proxy deve usar cookies do navegador, NÃO headers Authorization
            config.withCredentials = true; // Proxy vai repassar cookies

            // REMOVER qualquer header Authorization para usar apenas cookies
            delete config.headers.Authorization;
            delete config.headers.authorization;

            logger.debug('🔧 API interna via proxy (temporário)', {
              url: config.url,
              withCredentials: true,
              authHeaders: 'REMOVIDOS (usando cookies)',
              note: 'Proxy temporário devido a CORS'
            });
          } else {
            // URL real, usar cookies HttpOnly
            config.withCredentials = true;

            delete config.headers.Authorization;
            delete config.headers.authorization;

            logger.debug('🍪 API interna configurada para cookies HttpOnly exclusivos', {
              url: config.url,
              withCredentials: true,
              authHeaders: 'REMOVIDOS'
            });
          }
        } else {
          config.withCredentials = false;

          logger.debug('🌐 API externa configurada sem cookies', {
            url: config.url,
            withCredentials: false
          });
        }

        if (!config.headers['Content-Type']) {
          config.headers['Content-Type'] = DEFAULT_VALUES.CONTENT_TYPE;
        }

        logger.debug('Request interceptor configurado', {
          url: config.url,
          withCredentials: config.withCredentials,
          hasAuth: !!config.headers.Authorization,
          isInternal: isInternalAPI
        });

        return config;
      },
      (error) => {
        logger.error('Erro no request interceptor', { error: error.message });
        return Promise.reject(error);
      }
    );

    axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          logger.warn('Erro 401 detectado - tentando refresh do token', {
            url: originalRequest.url,
            method: originalRequest.method
          });

          try {
            // Tentar refresh apenas se não for uma URL de auth
            if (!originalRequest.url.includes('/auth/')) {
              await this.refreshToken();
              logger.info('Token renovado automaticamente, repetindo requisição');
              return axios.request(originalRequest);
            } else {
              logger.warn('Erro 401 em endpoint de auth - não tentando refresh');
            }
          } catch (refreshError) {
            logger.warn('Falha na renovação automática', {
              error: refreshError.message,
              url: originalRequest.url
            });
            // Não fazer logout automático para evitar loops
          }
        }

        // Log de outros erros para debugging
        if (error.response?.status !== 401) {
          const logLevel = error.response?.status >= 500 ? 'error' : 'debug';
          logger[logLevel]('Erro na requisição', {
            status: error.response?.status,
            url: error.config?.url,
            method: error.config?.method,
            message: error.message,
            data: error.response?.data
          });

          // Para erros 500, adicionar contexto adicional
          if (error.response?.status >= 500) {
            logger.error('Erro de servidor detectado', {
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent,
              url: window.location.href,
              apiUrl: error.config?.url,
              requestData: error.config?.data
            });
          }
        }

        return Promise.reject(error);
      }
    );

    this.interceptorsConfigured = true;
    logger.info('Interceptors axios configurados com sucesso');
  }

  /**
   * Autentica usuário e define token em cookie HttpOnly
   */
  async authenticate(code) {
    try {
      if (!code) {
        throw new Error('Código de autenticação não fornecido');
      }

      logger.info('Iniciando autenticação', { codeLength: code.length });

      // 1. Obter token do Cognito (processo existente)
      const cognitoResponse = await axios.post(
        config.cognitoUrl,
        { code },
        {
          withCredentials: false, // Cognito não precisa de cookies
          timeout: AUTH_TIMEOUTS.REQUEST_TIMEOUT
        }
      );

      if (!cognitoResponse.data?.data?.id_token) {
        throw new Error('Token não encontrado na resposta do Cognito');
      }

      const { id_token } = cognitoResponse.data.data;

      // 2. Validar token
      const payload = JSON.parse(atob(id_token.split('.')[1]));
      if (!payload.email) {
        throw new Error('Email não encontrado no token');
      }

      const userData = {
        email: payload.email.replace(/^azuread_/, ''),
        username: payload.email.replace(/^azuread_/, '').split('@')[0],
        name: payload.email.replace(/^azuread_/, '').split('@')[0]
      };

      // 3. Definir token em cookie HttpOnly via novo backend
      if (this.isHttpOnlySupported) {
        await this.setHttpOnlyToken(id_token);
        logger.info('Token definido em cookie HttpOnly via backend');
      } else {
        this.setManualCookie(id_token);
        logger.warn('Usando cookie manual como fallback');
      }

      // 4. Armazenar dados do usuário (não sensíveis)
      this.setUserData(userData);

      logger.info('Autenticação concluída com sucesso', {
        email: userData.email,
        httpOnly: this.isHttpOnlySupported
      });

      return { token: id_token, user: userData };

    } catch (error) {
      logger.error('Falha na autenticação', {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Define token em cookie HttpOnly via novo backend
   */
  async setHttpOnlyToken(token) {
    try {
      const response = await axios.post(`${this.baseURL}/auth/set-token`, 
        { token },
        {
          withCredentials: true,
          timeout: AUTH_TIMEOUTS.REQUEST_TIMEOUT
        }
      );
      
      logger.info('Token definido via endpoint HttpOnly', {
        user: response.data.user?.email,
        expiresIn: response.data.expiresIn
      });
      
      // Limpar tokens do localStorage
      this.clearLegacyTokens();
      
      return response.data;
    } catch (error) {
      logger.error('Falha ao definir cookie HttpOnly', {
        error: error.message,
        status: error.response?.status
      });
      
      // Fallback para cookie manual
      this.setManualCookie(token);
      throw error;
    }
  }

  /**
   * Renova token usando refresh token
   */
  async refreshToken() {
    try {
      const response = await axios.post(`${this.baseURL}/auth/refresh`, {}, {
        withCredentials: true,
        timeout: 5000
      });
      
      logger.info('Token renovado com sucesso', {
        user: response.data.user?.email,
        expiresIn: response.data.expiresIn
      });
      
      return response.data;
    } catch (error) {
      logger.error('Falha na renovação do token', {
        error: error.message,
        status: error.response?.status
      });
      throw error;
    }
  }

  /**
   * Verifica se usuário está autenticado
   */
  async isAuthenticated() {
    try {
      if (this.isHttpOnlySupported) {
        // Verificar via backend
        const response = await axios.get(`${this.baseURL}/auth/verify`, {
          withCredentials: true,
          timeout: 5000
        });
        
        const { authenticated, user, tokenStatus } = response.data;
        
        if (authenticated && user) {
          this.setUserData(user);
          
          logger.debug('Usuário autenticado via backend', {
            email: user.email,
            needsRefresh: tokenStatus?.needsRefresh
          });
        }
        
        return authenticated;
      } else {
        // Verificar cookie manual
        const token = this.getTokenFromCookie();
        return !!token;
      }
    } catch (error) {
      logger.debug('Verificação de autenticação falhou', {
        error: error.message,
        status: error.response?.status
      });
      return false;
    }
  }

  /**
   * Faz logout do usuário
   */
  async logout() {
    try {
      if (this.isHttpOnlySupported) {
        await axios.post(`${this.baseURL}/auth/logout`, {}, {
          withCredentials: true,
          timeout: 5000
        });
        
        logger.info('Logout realizado via backend');
      } else {
        this.clearManualCookie();
        logger.info('Logout realizado com limpeza manual de cookies');
      }
      
      this.clearAllData();
      
    } catch (error) {
      logger.error('Erro no logout', { error: error.message });
      this.clearAllData();
    }
  }

  /**
   * Cookie manual como fallback
   */
  setManualCookie(token) {
    const expires = new Date();
    expires.setTime(expires.getTime() + COOKIE_CONFIG.MAX_AGE);

    const isSecureContext = window.location.protocol === 'https:' || 
                           window.location.hostname === 'localhost';
    
    const cookieConfig = [
      `${COOKIE_CONFIG.TOKEN_NAME}=${token}`,
      `expires=${expires.toUTCString()}`,
      `path=${COOKIE_CONFIG.PATH}`,
      isSecureContext && COOKIE_CONFIG.SECURE ? 'secure' : '',
      `samesite=${COOKIE_CONFIG.SAME_SITE}`
    ].filter(Boolean).join('; ');

    document.cookie = cookieConfig;
    this.clearLegacyTokens();
    
    logger.warn('Token definido em cookie manual (fallback)');
  }

  /**
   * Obtém token do cookie (fallback)
   */
  getTokenFromCookie() {
    const cookies = document.cookie.split(';');
    const tokenCookie = cookies.find(cookie =>
      cookie.trim().startsWith(`${COOKIE_CONFIG.TOKEN_NAME}=`)
    );

    if (tokenCookie) {
      const token = tokenCookie.split('=')[1];
      if (token && token !== 'undefined' && token !== 'null') {
        return token;
      }
    }

    return null;
  }

  /**
   * Limpa cookie manual
   */
  clearManualCookie() {
    const isSecureContext = window.location.protocol === 'https:' || 
                           window.location.hostname === 'localhost';
    
    const clearCookieConfig = [
      `${COOKIE_CONFIG.TOKEN_NAME}=`,
      'expires=Thu, 01 Jan 1970 00:00:00 UTC',
      `path=${COOKIE_CONFIG.PATH}`,
      isSecureContext && COOKIE_CONFIG.SECURE ? 'secure' : '',
      `samesite=${COOKIE_CONFIG.SAME_SITE}`
    ].filter(Boolean).join('; ');

    document.cookie = clearCookieConfig;
  }

  /**
   * Define dados do usuário
   */
  setUserData(userData) {
    localStorage.setItem(STORAGE_KEYS.DSM_EMAIL, userData.email);
    localStorage.setItem(STORAGE_KEYS.DSM_NAME, userData.name);
    localStorage.setItem(STORAGE_KEYS.DSM_USERNAME, userData.username);
    localStorage.setItem(STORAGE_KEYS.DSM_TIME, new Date().toISOString());

    // Armazenar permissões se disponíveis
    if (userData.permissions) {
      localStorage.setItem(STORAGE_KEYS.DSM_PERMISSION, JSON.stringify(userData.permissions));
    }
    if (userData.role) {
      localStorage.setItem('@dsm/role', userData.role);
    }
  }

  /**
   * Obtém dados do usuário
   */
  getUserData() {
    const permissionData = localStorage.getItem(STORAGE_KEYS.DSM_PERMISSION);
    let permissions = null;

    try {
      permissions = permissionData ? JSON.parse(permissionData) : null;
    } catch (error) {
      permissions = permissionData;
    }

    return {
      email: localStorage.getItem(STORAGE_KEYS.DSM_EMAIL),
      name: localStorage.getItem(STORAGE_KEYS.DSM_NAME),
      username: localStorage.getItem(STORAGE_KEYS.DSM_USERNAME),
      permission: permissions,
      role: localStorage.getItem('@dsm/role'),
      time: localStorage.getItem(STORAGE_KEYS.DSM_TIME)
    };
  }

  /**
   * Define permissão do usuário
   */
  setUserPermission(permission) {
    if (typeof permission === 'object') {
      localStorage.setItem(STORAGE_KEYS.DSM_PERMISSION, JSON.stringify(permission));
    } else {
      localStorage.setItem(STORAGE_KEYS.DSM_PERMISSION, permission);
    }
  }

  /**
   * Verifica se usuário tem permissão específica
   */
  hasPermission(requiredPermission) {
    const userData = this.getUserData();

    if (!userData.permission) {
      return false;
    }

    // Se permission é array (novo formato)
    if (Array.isArray(userData.permission)) {
      return userData.permission.includes(requiredPermission);
    }

    // Se permission é string (formato antigo)
    if (typeof userData.permission === 'string') {
      return userData.permission === requiredPermission || userData.permission === 'admin';
    }

    return false;
  }

  /**
   * Limpa tokens legados
   */
  clearLegacyTokens() {
    const legacyKeys = [
      STORAGE_KEYS.JWT,
      STORAGE_KEYS.DSM_TOKEN,
      'token',
      'authToken',
      'access_token'
    ];

    legacyKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        logger.debug(`Removendo token legado: ${key}`);
        localStorage.removeItem(key);
      }
    });
  }

  /**
   * Limpa todos os dados
   */
  clearAllData() {
    this.clearLegacyTokens();

    const userDataKeys = [
      STORAGE_KEYS.DSM_EMAIL,
      STORAGE_KEYS.DSM_NAME,
      STORAGE_KEYS.DSM_USERNAME,
      STORAGE_KEYS.DSM_PERMISSION,
      STORAGE_KEYS.DSM_TIME,
      '@dsm/role'
    ];

    userDataKeys.forEach(key => {
      localStorage.removeItem(key);
    });
  }

  /**
   * Obtém headers de autorização (para compatibilidade)
   */
  getAuthHeaders() {
    // Com cookies HttpOnly, não precisamos de headers Authorization
    // Mas mantemos para compatibilidade com código existente
    const token = this.getTokenFromCookie();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  /**
   * Obtém headers legados (para compatibilidade)
   */
  getLegacyAuthHeaders() {
    const token = this.getTokenFromCookie();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  /**
   * Força verificação do status de autenticação
   */
  async forceAuthCheck() {
    try {
      await this.checkHttpOnlySupport();
      return await this.isAuthenticated();
    } catch (error) {
      logger.error('Erro na verificação forçada de autenticação', {
        error: error.message
      });
      return false;
    }
  }

  /**
   * Obtém status detalhado da autenticação
   */
  async getAuthStatus() {
    try {
      if (!this.isHttpOnlySupported) {
        return {
          authenticated: !!this.getTokenFromCookie(),
          method: 'manual-cookie',
          user: this.getUserData()
        };
      }

      const response = await axios.get(`${this.baseURL}/auth/verify`, {
        withCredentials: true,
        timeout: 5000
      });

      return {
        authenticated: response.data.authenticated,
        method: 'httponly-backend',
        user: response.data.user,
        tokenStatus: response.data.tokenStatus,
        timestamp: response.data.timestamp
      };
    } catch (error) {
      logger.error('Erro ao obter status de autenticação', {
        error: error.message
      });

      return {
        authenticated: false,
        method: 'error',
        error: error.message
      };
    }
  }
}

export const httpOnlyAuthService = new HttpOnlyAuthService();
export default httpOnlyAuthService;
