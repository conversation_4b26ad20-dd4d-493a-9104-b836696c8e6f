/**
 * Validação de variáveis de ambiente obrigatórias
 */

const requiredEnvVars = [
  'REACT_APP_API_PERMISSION',
  'REACT_APP_COGNITO_PARSE',
  'REACT_APP_STAGE'
];

const optionalEnvVars = [
  'REACT_APP_SENTRY_DSN',
  'REACT_APP_ANALYTICS_ID',
  'REACT_APP_API_JIRA_BASE_URL'
];

const sensitiveVars = [
  'REACT_APP_GOOGLE_KEY',
  'REACT_APP_AWS_ACCOUNT_ID',
  'REACT_APP_AWS_SECRET_ID',
  'REACT_APP_MS_TEAMS_WEBHOOK_URL'
];

/**
 * Valida se todas as variáveis de ambiente obrigatórias estão definidas
 * @param {boolean} silent - Se true, não exibe logs informativos (apenas erros)
 * @throws {Error} Se alguma variável obrigatória estiver ausente
 */
export const validateEnvironment = (silent = true) => {
  const missing = requiredEnvVars.filter(
    varName => !process.env[varName] || process.env[varName].trim() === ''
  );

  if (missing.length > 0) {
    const errorMessage = `❌ Variáveis de ambiente obrigatórias ausentes: ${missing.join(', ')}`;

    // Sempre mostrar logs quando há erro
    console.error('💥 Falha na validação de ambiente:');
    requiredEnvVars.forEach(varName => {
      const value = process.env[varName];
      const status = value ? '✅' : '❌';
      const maskedValue = value ? `${value.substring(0, 10)}...` : 'AUSENTE';
      console.error(`  ${status} ${varName}: ${maskedValue}`);
    });

    throw new Error(errorMessage);
  }

  // Verificar se variáveis sensíveis estão definidas no frontend
  if (process.env.NODE_ENV === 'development') {
    const presentSensitiveVars = sensitiveVars.filter(
      varName => process.env[varName]
    );
    
    if (presentSensitiveVars.length > 0) {
      console.warn(`⚠️ ALERTA DE SEGURANÇA: Variáveis sensíveis detectadas no frontend: ${presentSensitiveVars.join(', ')}`);
      console.warn('Estas variáveis devem ser movidas para o backend e acessadas via API segura');
      console.warn('Se não der estes warns sem ser em LOCAL, perfeito. Por hora ignorar.');
    }
  }
};

/**
 * Obter URL correta da API baseada no ambiente
 * SEMPRE usar API real para autenticação - nunca localhost
 */
const getCorrectApiUrl = () => {
  const stage = process.env.REACT_APP_STAGE || 'dev';
  const environment = process.env.NODE_ENV;

  // SEMPRE usar API real para autenticação - nunca localhost
  const apiUrls = {
    development: 'https://api.dsm.darede.com.br/dev', // Mesmo em dev local, usar API real
    dev: 'https://api.dsm.darede.com.br/dev',
    hml: 'https://api.dsm.darede.com.br/hml',
    production: 'https://api.dsm.darede.com.br/prod'
  };

  const apiUrl = apiUrls[stage] || apiUrls.dev;

  console.log('🌐 URL da API determinada (SEMPRE API REAL)', {
    stage,
    environment,
    apiUrl,
    note: 'Nunca usar localhost para autenticação'
  });

  return apiUrl;
};

/**
 * Configuração centralizada da aplicação
 */
export const config = {
  // URLs de API - SEMPRE usar API real
  apiUrl: getCorrectApiUrl(),
  cognitoUrl: process.env.REACT_APP_COGNITO_PARSE,

  // Ambiente
  stage: process.env.REACT_APP_STAGE,
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',

  // Features opcionais
  sentryDsn: process.env.REACT_APP_SENTRY_DSN,
  analyticsId: process.env.REACT_APP_ANALYTICS_ID,

  // Configurações de desenvolvimento
  enableDebugLogs: process.env.NODE_ENV === 'development',
  enableErrorSuppression: process.env.NODE_ENV === 'production'
};

/**
 * Valida configuração específica do ambiente
 * @param {boolean} silent - Se true, não exibe logs informativos (apenas erros)
 */
export const validateStageConfig = (silent = true) => {
  const stage = config.stage?.toLowerCase();

  const validStages = ['dev', 'development', 'hml', 'homolog', 'prod', 'production'];

  if (!validStages.includes(stage)) {
    console.warn(`⚠️ Stage '${stage}' não reconhecido. Stages válidos: ${validStages.join(', ')}`);
  }

  // Validações específicas por ambiente
  if (config.isProduction && !config.sentryDsn) {
    console.warn('⚠️ Sentry DSN não configurado para ambiente de produção');
  }

  // Logs informativos removidos para produção
};

// Executar validação na importação (modo silencioso - só mostra erros)
try {
  validateEnvironment(true); // silent = true
  validateStageConfig(true); // silent = true
} catch (error) {
  console.error('💥 Falha na validação de ambiente:', error.message);
  // Em desenvolvimento, mostrar erro mais detalhado
  if (process.env.NODE_ENV === 'development') {
    console.error('📋 Verifique o arquivo .env e certifique-se de que todas as variáveis estão definidas');
  }
}


