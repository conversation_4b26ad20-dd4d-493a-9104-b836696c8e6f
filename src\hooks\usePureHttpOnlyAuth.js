/**
 * Hook para autenticação EXCLUSIVAMENTE HttpOnly
 * Implementação 100% segura sem Bearer tokens
 */

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { usePureHttpOnlyAuth } from '../contexts/PureHttpOnlyAuthProvider';
import { logger } from '../utils/logger';
import { AUTH_ROUTES, AUTH_STATUS } from '../constants/auth';

/**
 * Hook principal de autenticação HttpOnly
 */
export const useHttpOnlyAuth = () => {
  const context = usePureHttpOnlyAuth();
  const navigate = useNavigate();
  const location = useLocation();

  /**
   * Login com redirecionamento automático
   */
  const loginWithRedirect = useCallback(async (code, redirectTo) => {
    try {
      // logger.info('🔐 Login com redirecionamento via HttpOnly', { 
      //   codeLength: code.length,
      //   redirectTo 
      // });

      const result = await context.login(code);
      
      // Redirecionar para página desejada ou home
      const targetPath = redirectTo || 
                        location.state?.from || 
                        AUTH_ROUTES.HOME;
      
      navigate(targetPath, { replace: true });
      
      // logger.info('✅ Login e redirecionamento concluídos', { targetPath });
      
      return result;
    } catch (error) {
      logger.error('❌ Erro no login com redirecionamento', { error: error.message });
      throw error;
    }
  }, [context, navigate, location.state]);

  /**
   * Logout com redirecionamento
   */
  const logoutWithRedirect = useCallback(async () => {
    try {
      // logger.info('🚪 Logout com redirecionamento via HttpOnly');
      
      await context.logout();
      navigate(AUTH_ROUTES.LOGIN, { replace: true });
      
      // logger.info('✅ Logout e redirecionamento concluídos');
    } catch (error) {
      logger.error('❌ Erro no logout com redirecionamento', { error: error.message });
      // Mesmo com erro, redirecionar
      navigate(AUTH_ROUTES.LOGIN, { replace: true });
    }
  }, [context, navigate]);

  /**
   * Verificar se precisa de autenticação
   */
  const requireAuth = useCallback((redirectTo = AUTH_ROUTES.LOGIN) => {
    if (!context.isAuthenticated && !context.isLoading) {
      logger.warn('🔒 Acesso negado - autenticação necessária');
      navigate(redirectTo, { 
        state: { from: location.pathname },
        replace: true 
      });
      return false;
    }
    return true;
  }, [context.isAuthenticated, context.isLoading, navigate, location.pathname]);

  /**
   * Verificar permissão com redirecionamento
   */
  const requirePermission = useCallback((permission, redirectTo = AUTH_ROUTES.UNAUTHORIZED) => {
    if (!context.isAuthenticated) {
      return requireAuth();
    }
    
    if (!context.hasPermission(permission)) {
      logger.warn('🚫 Acesso negado - permissão insuficiente', { 
        required: permission,
        userPermissions: context.user?.permissions 
      });
      navigate(redirectTo, { replace: true });
      return false;
    }
    
    return true;
  }, [context.isAuthenticated, context.hasPermission, context.user, requireAuth, navigate]);

  /**
   * Verificar múltiplas permissões
   */
  const requireAnyPermission = useCallback((permissions = [], redirectTo = AUTH_ROUTES.UNAUTHORIZED) => {
    if (!context.isAuthenticated) {
      return requireAuth();
    }
    
    if (!context.hasAnyPermission(permissions)) {
      logger.warn('🚫 Acesso negado - nenhuma permissão válida', { 
        required: permissions,
        userPermissions: context.user?.permissions 
      });
      navigate(redirectTo, { replace: true });
      return false;
    }
    
    return true;
  }, [context.isAuthenticated, context.hasAnyPermission, context.user, requireAuth, navigate]);

  /**
   * Redirecionar para login OAuth
   */
  const redirectToLogin = useCallback(() => {
    try {
      const stage = process.env.REACT_APP_STAGE || 'dev';
      const environment = process.env.NODE_ENV;
      
      let loginUrl;
      
      if (environment === 'development') {
        // Desenvolvimento - login direto
        loginUrl = "https://dsm-application.auth.us-east-1.amazoncognito.com/oauth2/authorize?client_id=3no5aulnqvut73n2hq93tqdrq0&response_type=code&scope=email+openid+phone&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fmfa";
      } else if (stage === 'dev') {
        // Dev
        loginUrl = "https://dsm-application.auth.us-east-1.amazoncognito.com/oauth2/authorize?identity_provider=AzureAD&redirect_uri=https://dev.dsm.darede.com.br/mfa&response_type=CODE&client_id=a612mhbbrvrh45ec6n8amqf5i&scope=aws.cognito.signin.user.admin%20email%20openid%20phone%20profile";
      } else if (stage === 'hml') {
        // Homologação
        loginUrl = "https://dsm-application.auth.us-east-1.amazoncognito.com/oauth2/authorize?identity_provider=AzureAD&redirect_uri=https://hml.dsm.darede.com.br/mfa&response_type=CODE&client_id=74k0l615eeerd40t2rb8tthffb&scope=aws.cognito.signin.user.admin%20email%20openid%20phone%20profile";
      } else {
        // Produção
        loginUrl = "https://dsm-application.auth.us-east-1.amazoncognito.com/oauth2/authorize?client_id=56abme2ar7cabfnep9919ijbed&response_type=code&scope=email+openid+phone&redirect_uri=https%3A%2F%2Fdsm.darede.com.br%2Fmfa";
      }
      
      // logger.info('🌐 Redirecionando para login OAuth', { 
      //   stage, 
      //   environment,
      //   loginUrl: loginUrl.substring(0, 100) + '...' 
      // });
      
      window.location.href = loginUrl; // não sei se é a melhor maneira
    } catch (error) {
      logger.error('❌ Erro ao redirecionar para login', { error: error.message });
      context.setError('Erro ao iniciar processo de login');
    }
  }, [context]);

  /**
   * Helpers memoizados
   */
  const authHelpers = useMemo(() => ({
    isLoggedIn: context.isAuthenticated,
    isLoggedOut: !context.isAuthenticated && !context.isLoading,
    isInitializing: context.isLoading,
    isReady: context.isInitialized && !context.isLoading,
    
    currentUser: context.user,
    userDisplayName: context.userName || context.userEmail,
    userAvatar: context.userInitials,
    
    // Permission helpers
    canAccess: context.hasPermission,
    canAccessAny: context.hasAnyPermission,
    canAccessAll: context.hasAllPermissions,
    
    // Session helpers
    sessionActive: context.isAuthenticated && !context.sessionWarning,
    sessionExpiring: context.sessionWarning,
    lastUserActivity: context.lastActivity,
    
    // Method helpers
    authMethod: 'httponly',
    isHttpOnlyAuth: true,
    isBearerAuth: false,
    isSecureAuth: true
  }), [
    context.isAuthenticated,
    context.isLoading,
    context.isInitialized,
    context.user,
    context.userName,
    context.userEmail,
    context.userInitials,
    context.hasPermission,
    context.hasAnyPermission,
    context.hasAllPermissions,
    context.sessionWarning,
    context.lastActivity
  ]);

  /**
   * Métodos de conveniência
   */
  const authMethods = useMemo(() => ({
    login: context.login,
    loginWithRedirect,
    redirectToLogin,
    
    logout: context.logout,
    logoutWithRedirect,
    
    checkAuth: context.checkAuth,
    refreshToken: context.refreshToken,
    
    // Guard methods
    requireAuth,
    requirePermission,
    requireAnyPermission,
    
    // Activity methods
    updateActivity: context.updateActivity,
    extendSession: context.extendSession
  }), [
    context.login,
    context.logout,
    context.checkAuth,
    context.refreshToken,
    context.updateActivity,
    context.extendSession,
    loginWithRedirect,
    logoutWithRedirect,
    redirectToLogin,
    requireAuth,
    requirePermission,
    requireAnyPermission
  ]);

  return {
    ...context,
    ...authHelpers,
    ...authMethods
  };
};

/**
 * Hook para verificação rápida de autenticação
 */
export const useHttpOnlyAuthStatus = () => {
  const { isAuthenticated, isLoading, status, isReady } = useHttpOnlyAuth();
  
  return useMemo(() => ({
    isAuthenticated,
    isLoading,
    isUnauthenticated: !isAuthenticated && !isLoading,
    isReady,
    status
  }), [isAuthenticated, isLoading, isReady, status]);
};

/**
 * Hook para verificação de permissões
 */
export const useHttpOnlyPermissions = (requiredPermissions = []) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, user } = useHttpOnlyAuth();
  
  return useMemo(() => ({
    hasPermission,
    hasAny: hasAnyPermission(requiredPermissions),
    hasAll: hasAllPermissions(requiredPermissions),
    userPermissions: user?.permissions || [],
    missing: requiredPermissions.filter(p => !hasPermission(p))
  }), [hasPermission, hasAnyPermission, hasAllPermissions, user, requiredPermissions]);
};

/**
 * Hook para guards de rota
 */
export const useHttpOnlyRouteGuard = () => {
  const { requireAuth, requirePermission, requireAnyPermission } = useHttpOnlyAuth();
  
  return {
    requireAuth,
    requirePermission,
    requireAnyPermission
  };
};

/**
 * Hook para informações do usuário
 */
export const useHttpOnlyCurrentUser = () => {
  const { 
    user, 
    userEmail, 
    userName, 
    userInitials,
    userDisplayName 
  } = useHttpOnlyAuth();
  
  return useMemo(() => ({
    user,
    email: userEmail,
    name: userName,
    displayName: userDisplayName,
    initials: userInitials,
    isLoaded: !!user
  }), [user, userEmail, userName, userDisplayName, userInitials]);
};

export default useHttpOnlyAuth;
