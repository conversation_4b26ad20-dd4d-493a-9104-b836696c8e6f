import axios from "axios";
import { authService } from "../services/authService";

export const dsmApiProvider = (jwt = "") => {
  // Se um JWT específico for fornecido, usa ele
  if (jwt) {
    return axios.create({
      baseURL: process.env.REACT_APP_API_PERMISSION || "",
      headers: {
        Authorization: `Bearer ${jwt}`,
      },
    });
  }

  // <PERSON><PERSON><PERSON> contr<PERSON>, usa o serviço de autenticação sem credentials
  return authService.createSimpleAxios(process.env.REACT_APP_API_PERMISSION || "");
};

export function getDsmHeader(tableName = "", jwt = "") {
  const token = jwt ? jwt : authService.getToken();

  let headers = {
    Authorization: `Bearer ${token}`,
  };

  if (tableName) {
    headers["dynamodb"] = tableName;
  }

  return headers;
}
