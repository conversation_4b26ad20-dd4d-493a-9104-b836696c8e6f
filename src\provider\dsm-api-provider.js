import axios from "axios";
import { authService } from "../services/authService";
import { getApiUrl } from "../utils/devConfig";

export const dsmApiProvider = (jwt = "") => {
  const apiUrl = getApiUrl();

  // Se um JWT específico for fornecido, usa ele
  if (jwt) {
    return axios.create({
      baseURL: apiUrl,
      headers: {
        Authorization: `Bearer ${jwt}`,
      },
    });
  }

  // Caso contrário, usa o serviço de autenticação sem credentials
  return authService.createSimpleAxios(apiUrl);
};

export function getDsmHeader(tableName = "", jwt = "") {
  const token = jwt ? jwt : authService.getToken();

  let headers = {
    Authorization: `Bearer ${token}`,
  };

  if (tableName) {
    headers["dynamodb"] = tableName;
  }

  return headers;
}
