/**
 * Testes para o CognitoAuthProvider
 * Demonstra como testar os componentes de autenticação
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { CognitoAuthProvider, useCognitoAuth } from '../contexts/CognitoAuthProvider';
import { useAuth } from '../hooks/useCognitoAuth';

// Mock do AWS Amplify
jest.mock('aws-amplify', () => ({
  Auth: {
    currentSession: jest.fn(),
    currentAuthenticatedUser: jest.fn(),
    signOut: jest.fn(),
  },
  Amplify: {
    configure: jest.fn(),
    Logger: {
      LOG_LEVEL: 'DEBUG'
    }
  }
}));

// Mock do httpOnlyAuthService
jest.mock('../services/httpOnlyAuthService', () => ({
  httpOnlyAuthService: {
    checkHttpOnlySupport: jest.fn().mockResolvedValue(true),
    authenticate: jest.fn(),
    logout: jest.fn(),
    isAuthenticated: jest.fn(),
    getCurrentUser: jest.fn(),
    setTokenCookie: jest.fn(),
    refreshToken: jest.fn()
  }
}));

// Mock do axios
jest.mock('axios', () => ({
  interceptors: {
    request: {
      use: jest.fn(),
      eject: jest.fn()
    },
    response: {
      use: jest.fn(),
      eject: jest.fn()
    }
  },
  post: jest.fn()
}));

// Componente de teste
const TestComponent = () => {
  const auth = useAuth();
  
  return (
    <div>
      <div data-testid="auth-status">
        {auth.isLoading ? 'loading' : auth.isAuthenticated ? 'authenticated' : 'unauthenticated'}
      </div>
      <div data-testid="user-email">{auth.userEmail || 'no-email'}</div>
      <div data-testid="auth-method">{auth.authMethod || 'no-method'}</div>
      <div data-testid="httponly-support">{auth.httpOnlySupported ? 'supported' : 'not-supported'}</div>
      
      <button 
        data-testid="login-button" 
        onClick={() => auth.login('test-code')}
      >
        Login
      </button>
      
      <button 
        data-testid="logout-button" 
        onClick={auth.logout}
      >
        Logout
      </button>
      
      <button 
        data-testid="check-permission" 
        onClick={() => auth.hasPermission('admin')}
      >
        Check Admin
      </button>
    </div>
  );
};

// Wrapper para testes
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <CognitoAuthProvider>
      {children}
    </CognitoAuthProvider>
  </BrowserRouter>
);

describe('CognitoAuthProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('deve renderizar estado inicial de loading', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('auth-status')).toHaveTextContent('loading');
  });

  test('deve verificar suporte HttpOnly na inicialização', async () => {
    const { httpOnlyAuthService } = require('../services/httpOnlyAuthService');
    
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(httpOnlyAuthService.checkHttpOnlySupport).toHaveBeenCalled();
    });
  });

  test('deve configurar interceptors do axios', async () => {
    const axios = require('axios');
    
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(axios.interceptors.request.use).toHaveBeenCalled();
      expect(axios.interceptors.response.use).toHaveBeenCalled();
    });
  });

  test('deve processar login com código', async () => {
    const { httpOnlyAuthService } = require('../services/httpOnlyAuthService');
    
    httpOnlyAuthService.authenticate.mockResolvedValue({
      user: {
        email: '<EMAIL>',
        name: 'Test User'
      },
      token: 'test-token'
    });

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    const loginButton = screen.getByTestId('login-button');
    fireEvent.click(loginButton);

    await waitFor(() => {
      expect(httpOnlyAuthService.authenticate).toHaveBeenCalledWith('test-code');
    });
  });

  test('deve processar logout', async () => {
    const { httpOnlyAuthService } = require('../services/httpOnlyAuthService');
    
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    const logoutButton = screen.getByTestId('logout-button');
    fireEvent.click(logoutButton);

    await waitFor(() => {
      expect(httpOnlyAuthService.logout).toHaveBeenCalled();
    });
  });

  test('deve verificar permissões do usuário', async () => {
    // Mock de usuário autenticado com permissões
    const mockUser = {
      email: '<EMAIL>',
      name: 'Admin User',
      permissions: ['admin', 'user']
    };

    const TestPermissionComponent = () => {
      const auth = useAuth();
      
      // Simular usuário autenticado
      React.useEffect(() => {
        if (auth.isAuthenticated) {
          // Usuário já está autenticado
        }
      }, [auth.isAuthenticated]);
      
      return (
        <div>
          <div data-testid="has-admin">
            {auth.hasPermission('admin') ? 'has-admin' : 'no-admin'}
          </div>
          <div data-testid="has-user">
            {auth.hasPermission('user') ? 'has-user' : 'no-user'}
          </div>
          <div data-testid="has-manager">
            {auth.hasPermission('manager') ? 'has-manager' : 'no-manager'}
          </div>
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestPermissionComponent />
      </TestWrapper>
    );

    // Aguardar renderização
    await waitFor(() => {
      expect(screen.getByTestId('has-admin')).toBeInTheDocument();
    });
  });

  test('deve lidar com erros de autenticação', async () => {
    const { httpOnlyAuthService } = require('../services/httpOnlyAuthService');
    
    httpOnlyAuthService.authenticate.mockRejectedValue(
      new Error('Authentication failed')
    );

    const TestErrorComponent = () => {
      const auth = useAuth();
      
      return (
        <div>
          <div data-testid="error-message">
            {auth.error || 'no-error'}
          </div>
          <button 
            data-testid="trigger-error" 
            onClick={() => auth.login('invalid-code')}
          >
            Trigger Error
          </button>
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestErrorComponent />
      </TestWrapper>
    );

    const triggerButton = screen.getByTestId('trigger-error');
    fireEvent.click(triggerButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('Authentication failed');
    });
  });

  test('deve refresh token automaticamente', async () => {
    const { httpOnlyAuthService } = require('../services/httpOnlyAuthService');
    
    httpOnlyAuthService.refreshToken.mockResolvedValue(true);

    const TestRefreshComponent = () => {
      const auth = useAuth();
      
      return (
        <div>
          <button 
            data-testid="refresh-token" 
            onClick={auth.refreshToken}
          >
            Refresh Token
          </button>
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestRefreshComponent />
      </TestWrapper>
    );

    const refreshButton = screen.getByTestId('refresh-token');
    fireEvent.click(refreshButton);

    await waitFor(() => {
      expect(httpOnlyAuthService.refreshToken).toHaveBeenCalled();
    });
  });
});

describe('useAuth Hook', () => {
  test('deve fornecer métodos de autenticação', () => {
    const TestHookComponent = () => {
      const auth = useAuth();
      
      return (
        <div>
          <div data-testid="methods">
            {typeof auth.login === 'function' ? 'has-login' : 'no-login'}
            {typeof auth.logout === 'function' ? ',has-logout' : ',no-logout'}
            {typeof auth.hasPermission === 'function' ? ',has-permission' : ',no-permission'}
          </div>
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestHookComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('methods')).toHaveTextContent('has-login,has-logout,has-permission');
  });

  test('deve fornecer helpers de usuário', () => {
    const TestUserHelpersComponent = () => {
      const auth = useAuth();
      
      return (
        <div>
          <div data-testid="user-helpers">
            {auth.userEmail ? 'has-email' : 'no-email'}
            {auth.userName ? ',has-name' : ',no-name'}
            {auth.userInitials ? ',has-initials' : ',no-initials'}
          </div>
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestUserHelpersComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('user-helpers')).toBeInTheDocument();
  });
});

// Testes de integração
describe('Integração Completa', () => {
  test('deve completar fluxo de login completo', async () => {
    const { httpOnlyAuthService } = require('../services/httpOnlyAuthService');
    
    // Mock do fluxo completo
    httpOnlyAuthService.checkHttpOnlySupport.mockResolvedValue(true);
    httpOnlyAuthService.authenticate.mockResolvedValue({
      user: {
        email: '<EMAIL>',
        name: 'Test User',
        permissions: ['user']
      },
      token: 'jwt-token'
    });

    const IntegrationTestComponent = () => {
      const auth = useAuth();
      const [loginAttempted, setLoginAttempted] = React.useState(false);
      
      const handleLogin = async () => {
        setLoginAttempted(true);
        try {
          await auth.login('test-code');
        } catch (error) {
          console.error('Login failed:', error);
        }
      };
      
      return (
        <div>
          <div data-testid="integration-status">
            {auth.isLoading ? 'loading' : 
             auth.isAuthenticated ? 'authenticated' : 
             loginAttempted ? 'login-attempted' : 'ready'}
          </div>
          <div data-testid="integration-user">
            {auth.userEmail || 'no-user'}
          </div>
          <button 
            data-testid="integration-login" 
            onClick={handleLogin}
          >
            Login
          </button>
        </div>
      );
    };

    render(
      <TestWrapper>
        <IntegrationTestComponent />
      </TestWrapper>
    );

    // Aguardar inicialização
    await waitFor(() => {
      expect(screen.getByTestId('integration-status')).not.toHaveTextContent('loading');
    });

    // Executar login
    const loginButton = screen.getByTestId('integration-login');
    fireEvent.click(loginButton);

    // Verificar resultado
    await waitFor(() => {
      expect(httpOnlyAuthService.authenticate).toHaveBeenCalledWith('test-code');
    });
  });
});
