import React, { useEffect, useState } from "react";
import { Card, Col, Pagination, Row, Spin, Table, Typography } from "antd";
import { jiraGet, isJiraAvailable } from "../../../service/apiJira";
import { ProjectDetails } from "../../Table/ProjectDetails";
import Jira<PERSON>allback from "../../JiraFallback";
import "./ProjectByClient.css";
import "antd/dist/antd.css";

export const ProjectsByClient = (props) => {
  const { Title, Text } = Typography;
  const [allProjects, setAllProjects] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [currentPageNumber, setCurrentPageNumber] = useState(1);
  const [loading, setLoading] = useState(false);
  const [projectsTableVisible, setProjectsTableVisible] = useState(true);
  const [projectDetailsData, setProjectDetailsData] = useState([]);

  const columns = [
    // {
    //   title: 'Client',
    //   dataIndex: 'client',
    //   key: 'client',
    //   render: (client) => <p className="table-font">{client}</p>,
    // },
    {
      title: "Projeto",
      dataIndex: "name",
      key: "name",
      render: (name) => <Text>{name}</Text>,
    },
    {
      title: "Contratadas",
      dataIndex: "engagedTime",
      key: "engagedTime",
      render: (engagedTime) => (
        <Text>{(engagedTime / 3600).toFixed(2)} horas</Text>
      ),
    },
    {
      title: "Consumidas",
      dataIndex: "registeredTime",
      key: "registeredTime",
      render: (registeredTime) => (
        <Text>{(registeredTime / 3600).toFixed(2)} horas</Text>
      ),
    },
    {
      title: "Disponíveis",
      dataIndex: "availableTime",
      key: "availableTime",
      render: (availableTime) => (
        <Text>{(availableTime / 3600).toFixed(2)} horas</Text>
      ),
    },
  ];

  async function getAllProjects() {
    if (!isJiraAvailable()) {
      console.warn('Jira não está disponível. Funcionalidade de projetos desabilitada.');
      setAllProjects([]);
      setTableData([]);
      return;
    }

    const { data } = await jiraGet("read/projects");

    await paginateProjects(data.values, 1, 10);
    setAllProjects(data.values);
  }

  async function paginateProjects(projects, pageNumber, rowsNumber) {
    setLoading(true);

    const projectsPerPage = projects.slice(
      (pageNumber - 1) * rowsNumber,
      pageNumber * rowsNumber
    );
    setCurrentPageNumber(pageNumber);

    await tableFill(projectsPerPage);

    setLoading(false);
  }

  async function tableFill(projects) {
    for (let project of projects) {
      const { engagedTime, registeredTime, issues } = await getProjectTimes(
        project.id
      );

      project.engagedTime = engagedTime;
      project.registeredTime = registeredTime;
      project.issues = issues;

      const availableTime = engagedTime - registeredTime;

      if (availableTime < 0) {
        project.availableTime = 0;
      } else {
        project.availableTime = availableTime;
      }
    }

    setTableData(projects);
  }

  async function getProjectTimes(projectId) {
    const { data } = await jiraGet(`read/project/issues/${projectId}`);
    const { issues } = data;

    let engagedTime = 0;
    let registeredTime = 0;

    for (let issue of issues) {
      const { timeoriginalestimate, timespent } = issue.fields;

      if (timeoriginalestimate) {
        engagedTime += timeoriginalestimate;
      }

      if (timespent) {
        registeredTime += timespent;
      }
    }

    return { engagedTime, registeredTime, issues };
  }

  function toggleTable() {
    setProjectsTableVisible(!projectsTableVisible);
  }

  function onClickTable(record) {
    setProjectDetailsData(record);
    toggleTable();
  }

  useEffect(() => {
    getAllProjects();
  }, []);

  // Renderizar fallback se Jira não estiver disponível
  if (!isJiraAvailable()) {
    return (
      <JiraFallback
        title="Projetos por Cliente"
        message="A integração com Jira é necessária para exibir os projetos."
        showDetails={true}
      />
    );
  }

  return (
    <Card
      bordered={false}
      style={{
        borderRadius: "20px",
        height: "100%",
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
      }}
    >
      {loading && (
        <div className="loading">
          <Spin />
        </div>
      )}
      {projectsTableVisible ? (
        <>
          <Title level={4} style={{ fontWeight: 400 }}>
            Projetos por cliente
          </Title>
          <Table
            scroll={{ x: "100%" }}
            columns={columns}
            dataSource={tableData}
            pagination={false}
            onRow={(record) => {
              return { onClick: (event) => onClickTable(record) };
            }}
          />
          <Pagination
            onChange={(pageNumber, rowsNumber) =>
              paginateProjects(allProjects, pageNumber, rowsNumber)
            }
            defaultCurrent={1}
            className="paginator"
            current={currentPageNumber}
            size={100}
            responsive={true}
            total={allProjects.length}
          />
        </>
      ) : (
        <ProjectDetails data={projectDetailsData} toggleTable={toggleTable} />
      )}
    </Card>
  );
};
