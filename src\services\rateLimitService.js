import axios from 'axios';

/**
 * Serviço de Rate Limiting
 * Integra controle de taxa com as APIs existentes
 */
class RateLimitService {
  constructor() {
    this.limits = new Map();
    this.interceptors = new Map();
    this.setupAxiosInterceptors();
  }

  /**
   * Configura interceptors do axios para monitorar rate limits
   */
  setupAxiosInterceptors() {
    // Request interceptor
    const requestInterceptor = axios.interceptors.request.use(
      (config) => {
        // Adicionar timestamp da requisição
        config.metadata = { startTime: Date.now() };
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    const responseInterceptor = axios.interceptors.response.use(
      (response) => {
        this.handleRateLimitHeaders(response);
        return response;
      },
      (error) => {
        if (error.response) {
          this.handleRateLimitHeaders(error.response);
          
          // Tratar erro 429 (Too Many Requests)
          if (error.response.status === 429) {
            this.handleRateLimitExceeded(error.response);
          }
        }
        return Promise.reject(error);
      }
    );

    this.interceptors.set('request', requestInterceptor);
    this.interceptors.set('response', responseInterceptor);
  }

  /**
   * Processa headers de rate limit da resposta
   */
  handleRateLimitHeaders(response) {
    const headers = response.headers;
    const url = response.config?.url || 'unknown';
    
    const rateLimitInfo = {
      limit: parseInt(headers['x-ratelimit-limit']) || null,
      remaining: parseInt(headers['x-ratelimit-remaining']) || null,
      reset: headers['x-ratelimit-reset'] || null,
      window: parseInt(headers['x-ratelimit-window']) || null,
      retryAfter: parseInt(headers['retry-after']) || null,
      timestamp: Date.now()
    };

    // Armazenar informações de rate limit
    if (rateLimitInfo.limit !== null) {
      this.limits.set(url, rateLimitInfo);
      
      // Emitir evento para componentes interessados
      this.emitRateLimitUpdate(url, rateLimitInfo);
    }
  }

  /**
   * Trata quando o rate limit é excedido
   */
  handleRateLimitExceeded(response) {
    const url = response.config?.url || 'unknown';
    const retryAfter = parseInt(response.headers['retry-after']) || 60;
    

    
    // Emitir evento de limite excedido
    this.emitRateLimitExceeded(url, {
      retryAfter,
      response
    });
  }

  /**
   * Emite evento de atualização de rate limit
   */
  emitRateLimitUpdate(url, info) {
    const event = new CustomEvent('rateLimitUpdate', {
      detail: { url, info }
    });
    window.dispatchEvent(event);
  }

  /**
   * Emite evento de limite excedido
   */
  emitRateLimitExceeded(url, data) {
    const event = new CustomEvent('rateLimitExceeded', {
      detail: { url, ...data }
    });
    window.dispatchEvent(event);
  }

  /**
   * Obtém informações de rate limit para uma URL
   */
  getRateLimitInfo(url) {
    return this.limits.get(url) || null;
  }

  /**
   * Obtém todas as informações de rate limit
   */
  getAllRateLimitInfo() {
    return Object.fromEntries(this.limits);
  }

  /**
   * Verifica se uma URL está próxima do limite
   */
  isNearLimit(url, threshold = 0.8) {
    const info = this.getRateLimitInfo(url);
    if (!info || info.limit === null || info.remaining === null) {
      return false;
    }
    
    const usageRatio = (info.limit - info.remaining) / info.limit;
    return usageRatio >= threshold;
  }

  /**
   * Calcula tempo até o reset do rate limit
   */
  getTimeUntilReset(url) {
    const info = this.getRateLimitInfo(url);
    if (!info || !info.reset) {
      return null;
    }

    const resetTime = new Date(info.reset).getTime();
    const now = Date.now();
    return Math.max(0, resetTime - now);
  }

  /**
   * Cria um wrapper para funções que fazem requisições
   */
  createRateLimitedFunction(fn, options = {}) {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      exponentialBackoff = true
    } = options;

    return async (...args) => {
      let retries = 0;
      
      while (retries <= maxRetries) {
        try {
          return await fn(...args);
        } catch (error) {
          if (error.response?.status === 429 && retries < maxRetries) {
            const retryAfter = parseInt(error.response.headers['retry-after']) || retryDelay / 1000;
            const delay = exponentialBackoff 
              ? retryDelay * Math.pow(2, retries)
              : retryAfter * 1000;
            

            
            await new Promise(resolve => setTimeout(resolve, delay));
            retries++;
          } else {
            throw error;
          }
        }
      }
    };
  }

  /**
   * Cria uma instância do axios com rate limiting
   */
  createRateLimitedAxios(baseURL, options = {}) {
    const instance = axios.create({
      baseURL,
      ...options
    });

    // Adicionar interceptors específicos para esta instância
    instance.interceptors.request.use(
      (config) => {
        config.metadata = { startTime: Date.now() };
        return config;
      }
    );

    instance.interceptors.response.use(
      (response) => {
        this.handleRateLimitHeaders(response);
        return response;
      },
      async (error) => {
        if (error.response?.status === 429) {
          const retryAfter = parseInt(error.response.headers['retry-after']) || 60;
          
          if (options.autoRetry !== false) {
            console.log(`Rate limited. Auto-retrying in ${retryAfter} seconds...`);
            await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
            return instance.request(error.config);
          }
        }
        return Promise.reject(error);
      }
    );

    return instance;
  }

  /**
   * Monitora rate limits em tempo real
   */
  startMonitoring(interval = 30000) {
    this.monitoringInterval = setInterval(() => {
      const now = Date.now();
      
      // Limpar informações antigas
      for (const [url, info] of this.limits.entries()) {
        if (now - info.timestamp > 300000) { // 5 minutos
          this.limits.delete(url);
        }
      }
      
      // Emitir estatísticas
      this.emitMonitoringStats();
    }, interval);
  }

  /**
   * Para o monitoramento
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * Emite estatísticas de monitoramento
   */
  emitMonitoringStats() {
    const stats = {
      totalEndpoints: this.limits.size,
      nearLimitEndpoints: 0,
      exceededEndpoints: 0,
      details: []
    };

    for (const [url, info] of this.limits.entries()) {
      const detail = {
        url,
        usage: info.limit ? (info.limit - info.remaining) / info.limit : 0,
        remaining: info.remaining,
        limit: info.limit,
        resetTime: info.reset
      };

      if (detail.usage >= 0.9) {
        stats.nearLimitEndpoints++;
      }
      if (info.remaining === 0) {
        stats.exceededEndpoints++;
      }

      stats.details.push(detail);
    }

    const event = new CustomEvent('rateLimitStats', {
      detail: stats
    });
    window.dispatchEvent(event);
  }

  /**
   * Limpa todos os dados de rate limit
   */
  clear() {
    this.limits.clear();
  }

  /**
   * Destrói o serviço
   */
  destroy() {
    this.stopMonitoring();
    this.clear();
    
    // Remover interceptors
    for (const [type, id] of this.interceptors.entries()) {
      if (type === 'request') {
        axios.interceptors.request.eject(id);
      } else if (type === 'response') {
        axios.interceptors.response.eject(id);
      }
    }
    
    this.interceptors.clear();
  }
}

// Instância singleton
export const rateLimitService = new RateLimitService();

export default rateLimitService;
