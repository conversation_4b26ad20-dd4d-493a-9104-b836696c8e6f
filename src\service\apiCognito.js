import Axios from "axios";
import useSWR from "swr";
import { authService } from "../services/authService";
import { getApiUrl } from "../utils/devConfig";

export function useCognitoGet() {
  const apiUrl = getApiUrl();
  const { data, error, mutate } = useSWR(
    `${apiUrl}/cognito/read`,
    async () => {
      const { data } = await Axios.get(`${apiUrl}/cognito/read`);
      return data.data;
    }
  );

  return { data, error, mutate };
}

export const cognitoGet = async () => {
  const apiUrl = getApiUrl();
  const { data } = await Axios.get(`${apiUrl}/cognito/read`);
  return data;
};

export const cognitoPutStatus = async (body) => {
  const { data } = await Axios.put(
    process.env.REACT_APP_API_PERMISSION + "cognito/update/status",
    { ...body },
    {
      headers: authService.getLegacyAuthHeaders(),
    }
  );

  return data;
};

export const cognitoPutRole = async (body) => {
  const { data } = await Axios.put(
    process.env.REACT_APP_API_PERMISSION + "cognito/update/role",
    { ...body },
    {
      headers: authService.getLegacyAuthHeaders(),
    }
  );

  return data;
};
