import { useState, useEffect } from "react";
import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";

import { useDebounce } from "../../hooks/useDebouce";

export const SearchInput = ({
  onChange,
  placeholder,
  delay = 300,
  style,
  ...rest
}) => {
  const [search, setSearch] = useState("");

  const debouncedSearchTerm = useDebounce(search, delay);

  useEffect(() => {
    onChange(search);
  }, [debouncedSearchTerm]);

  return (
    <Input
      style={{ borderRadius: "5px", ...style }}
      type="text"
      prefix={<SearchOutlined />}
      placeholder={placeholder}
      onChange={(event) => setSearch(event.currentTarget.value)}
      {...rest}
    />
  );
};
