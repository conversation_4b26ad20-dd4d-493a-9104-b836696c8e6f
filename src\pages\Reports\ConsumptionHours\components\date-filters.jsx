import "moment/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";

import { Col, DatePicker, Typography } from "antd";
import { shallowEqual, useSelector } from "react-redux";
import { setConsumptionState } from "../../../../store/actions/consumption-hours-action";

export const DateFilters = () => {

    return (
        <>
            <DateField
                label={"Inicio:"}
                selector={"dtStart"}
                offset={1}
            />

            <DateField
                label={"Fim:"}
                selector={"dtEnd"}
                offset={1}
            />
        </>
    )
}

const DateField = ({ label, selector, offset = 0 }) => {
    const state = useSelector(state => state.consumption[selector], shallowEqual)

    return (
        <Col
            xl={{ span: 3, offset  }}
            lg={{ span: 6, offset }}
            md={{ span: 3, offset }}
            sm={{ span: 10, offset }}
            style={{ marginBottom: 10 }}
        >
            <Typography.Text>{label}</Typography.Text>
            <DatePicker
                placeholder="Selecione um mês"
                picker="month"
                style={{ width: "100%" }}
                locale={locale}
                onChange={(moment) => setConsumptionState({ field: selector, value: moment })}
                defaultValue={state}
            />
        </Col>
    )
}