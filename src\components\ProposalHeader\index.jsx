import React, { useEffect, useState, useContext } from "react";
import { Row, Col, Input, Typography, Select, Form } from "antd";
import { AddArchitect } from "../../components/Modals/TechnicalProposals/AddArchitect";
import { AddBU } from "../../components/Modals/TechnicalProposals/AddBU";
import { AddContact } from "../../components/Modals/TechnicalProposals/AddContact";
import { AddOpportunity } from "../../components/Modals/TechnicalProposals/AddOportunity";
import { useLocation } from "react-router-dom";
import axios from "axios";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { SelectImage } from "../SelectImage";
import { apiProposals, getHeader } from "../../utils/api";
import Cookies from "js-cookie";
import { ProposalContext } from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";
import { getCustomerNameOption } from "../../utils/getCustomerNameOption";
import { getCustomerBrandNameStringSync } from "../../utils/getCustomerBrandNameString";
import { authService } from "../../services/authService";

export const ProposalHeader = (proposalProps) => {
  const { proposalState, setProposalState } = useContext(ProposalContext);
  const { state } = useLocation();
  const { Option } = Select;
  const { Title } = Typography;
  const [allCustomers, setAllCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(
    state !== null && !window.location.href.includes("add")
      ? {
          names: {
            name: state?.customer?.names?.name
              ? state?.customer?.names?.name
              : "",
            fantasy_name: state?.customer?.names?.fantasy_name
              ? state?.customer?.names?.fantasy_name
              : "",
          },
          contacts: state?.customer?.contacts ? state.customer.contacts : [],
          cnpj: state?.customer?.cnpj ? state?.customer?.cnpj : "",
        }
      : {
          names: { name: "", fantasy_name: "" },
          contacts: [],
          cnpj: "",
        }
  );

  const [contacts, setContacts] = useState(
    selectedCustomer ? selectedCustomer.contacts : []
  );
  const [types, setTypes] = useState([]);
  const [selectedType, setSelectedType] = useState("");
  const [status, setStatus] = useState([
    "não inicializada",
    "em andamento",
    "concluída",
    "revisão técnica",
    "revisada",
  ]);

  const [oportunities, setOportunities] = useState(() => {
    let opportunities = [];
    if (state?.opportunity) {
      opportunities = state.opportunity;
    }

    if (
      !window.location.href.includes("add") &&
      state?.mainOportunity &&
      state?.mainOportunity !== ""
    ) {
      if (!opportunities.find((o) => o === state?.mainOportunity)) {
        opportunities = [...opportunities, state.mainOportunity];
      }
    }

    return opportunities;
  });
  const [architects, setArchitects] = useState([]);
  const [bus, setBus] = useState([]);
  const [loading, setLoading] = useState(false);
  const [customerImage, setCustomerImage] = useState("");
  const [recognizingHeaderChange, setRecognizingHeaderChange] = useState(false);

  function getCustomerName() {
    let clientName = "";
    if (state) {
      if (state?.customer?.names) {
        if (state.customer.names.fantasy_name)
          clientName = state.customer.names.fantasy_name;
        else clientName = state.customer.names.name;
      }

      if (state?.customer?.name) clientName = state.customer.name;
    }

    return clientName;
  }

  const getCookieValues = (formName) => {
    const CookieValues = Cookies.get("header_cookie");

    let formatedCookieValues;

    if (window.location.href.includes("add")) {
      if (CookieValues !== "[object Object]" && CookieValues) {
        formatedCookieValues = JSON.parse(CookieValues);

        if (formatedCookieValues[formName] === null) {
          return "";
        } else {
          return formatedCookieValues[formName];
        }
      } else if (
        formName === "project_name" ||
        formName === "project_type" ||
        formName === "projectStatus" ||
        formName === "projectOpportunity"
      ) {
        formatedCookieValues = "";
      } else if (formName === "projectClient") {
        formatedCookieValues = {};
      } else {
        formatedCookieValues = [];
      }
    }
  };

  const [selectedStatus, setSelectedStatus] = useState(() => {
    if (window.location.href.includes("add")) {
      return null;
    }
    if (window.location.href.includes("commercial")) {
      if (getCookieValues("projectStatus")) {
        return getCookieValues("projectStatus");
      } else {
        return state.commercialStatus;
      }
    } else {
      if (getCookieValues("projectStatus")) {
        return getCookieValues("projectStatus");
      } else {
        return state.status;
      }
    }
  });

  const headerInputs = [
    {
      title: "Nome do Projeto",
      placeholder: "Título da proposta",
      inputType: "Input",
      formName: "project_name",
      value: state ? state.name : "",
    },
    {
      title: "Cliente",
      id: "projectClient",
      placeholder: "Nome fantasia do cliente",
      inputType: "Select",
      formName: "client_name",
      value: state?.customer ? getCustomerName(state?.customer) : null,
    },
    {
      title: "Tipo",
      placeholder: "Tipo da proposta",
      inputType: "Select",
      formName: "project_type",
      value: state ? state.type : null,
    },
    {
      title: window.location.href.includes("commercial")
        ? "Status Comercial"
        : "Status",
      placeholder: "Status da proposta",
      inputType: "Select",
      formName: "project_status",
      value: selectedStatus,
    },
  ];

  const mainContact = selectedCustomer.contacts.find(
    (c, i) => `${i}` === localStorage.getItem("technical-proposal/main-contact")
  );

  const headerInputsWithList = [
    {
      title: "Contatos",
      placeholder: "Nome do contato",
      inputType: "Select",
      formName: "project_contacts",
      component: <AddContact />,
      value: mainContact?.names?.first_name || null,
    },
    {
      title: "Arquitetos",
      placeholder: "Nome do arquiteto",
      inputType: "Select",
      formName: "project_architects",
      component: <AddArchitect />,
      value: state ? state.architects : [],
    },
    {
      title: "BUs",
      placeholder: "Nome da unidade de negócio",
      inputType: "Select",
      formName: "project_bu",
      component: <AddBU state={state?.bus} />,
      value: state ? state.bus : [],
    },
    {
      title: "Oportunidade",
      placeholder: "Número da oportunidade",
      inputType: "Select",
      formName: "project_opportunity",
      component: <AddOpportunity />,
      value: state ? state.mainOportunity : null,
    },
  ];

  const handleSelectCustomer = (value) => {
    const customer = allCustomers?.find(
      (c) => c?.names?.fantasy_name === value
    );
    if (customer === undefined) {
      const customerFantasyName = allCustomers?.find(
        (c) => c?.names?.name === value
      );

      setSelectedCustomer(customerFantasyName);
    } else {
      setProposalState({
        type: TotalValuesProposalType.SET_CUSTOMER,
        value: customer,
      });
      setSelectedCustomer(customer);
      getCustomerBrand(customer);
    }
  };

  useEffect(() => {
    setSelectedCustomer({
      ...selectedCustomer,
      contacts: contacts,
    });
  }, [contacts]);

  useEffect(() => {
    setProposalState({
      type: TotalValuesProposalType.SET_OPORTUNITIES,
      value: oportunities,
    });
  }, [oportunities]);

  useEffect(() => {
    if (proposalProps) {
      proposalProps?.setCurrentContacts(selectedCustomer);
      setContacts(selectedCustomer.contacts);
    }
  }, [selectedCustomer]);

  useEffect(() => {
    setLoading(true);

    async function getCustomers() {
      const tableCustomers = await dynamoGet(
        `${process.env.REACT_APP_STAGE}-customers`
      );

      const filterDuplicatedNames = [];

      tableCustomers.forEach((c) => {
        if (
          !filterDuplicatedNames.find(
            (t) => t?.names?.fantasy_name === c?.names?.fantasy_name
          )
        ) {
          filterDuplicatedNames.push(c);
        }
      });

      setAllCustomers(filterDuplicatedNames);
    }

    async function getUsers() {
      try {
        const { data } = await axios.get(
          `${process.env.REACT_APP_API_PERMISSION}cognito/read`,
          {
            headers: authService.getAuthHeaders(),
          }
        );
        let users = [];
        for (let i = 0; i < data.data.length; i++) {
          users.push(data.data[i].email);
        }
        setArchitects(users);
      } catch (error) {
        console.error('Erro ao buscar usuários:', error);
        setArchitects([]);
      }
    }

    async function getBus() {
      const response = await dynamoGet(
        `${process.env.REACT_APP_STAGE}-wallets`
      );
      let buNames = [];
      for (let i = 0; i < response.length; i++) {
        buNames.push(response[i].name);
      }

      setBus(buNames);
    }

    async function getTypes() {
      let response = await dynamoGet("hml-crm-custom-fields");

      const consumeTypes = response[0].deal.find(
        (deal) => deal.name === "Tipo de Consumo"
      );

      let labels = consumeTypes.options.map((option) => option.label);

      setTypes(labels);
    }

    async function getAllData() {
      await getCustomers();
      await getTypes();
      await getBus();
      await getUsers();
    }

    const clientName = getCustomerName();
    const isTemplate = window.location.href.includes("/add");

    if (clientName && !isTemplate) {
      const headers = getHeader();

      const name = getCustomerBrandNameStringSync(clientName);

      apiProposals
        .get(`/brand/${name}`, { headers })
        .then((response) => {
          const { url } = response.data;
          setCustomerImage(url);
        })
        .catch((error) => {
          console.log(error);
          setCustomerImage("");
        });
    }

    getAllData();
    setLoading(false);
  }, []);

  function getCustomerBrand(customer) {
    if (customer) {
      let clientName = "";

      if (customer.names) {
        if (customer.names.fantasy_name)
          clientName = customer.names.fantasy_name;
        else clientName = customer.names.name;
      }

      if (customer.name) clientName = customer.name;

      const headers = getHeader();

      const name = getCustomerBrandNameStringSync(clientName);

      apiProposals
        .get(`/brand/${name}`, { headers })
        .then((response) => {
          const { url } = response.data;
          setCustomerImage(url);
        })
        .catch((error) => {
          console.log(error);
          setCustomerImage("");
        });
    }
  }

  function onChangeImage(image) {
    setCustomerImage(image);
    if (proposalProps.setBrand && !window.location.href.includes("add")) {
      proposalProps.setBrand(image);
    }
  }

  return (
    <Col span={24}>
      <Row>
        <Col span={18}>
          <Row style={{ marginBottom: "15px" }} gutter={[4, 0]}>
            {headerInputs
              .filter(
                (input) =>
                  input.formName.includes("project_name") ||
                  input.formName.includes("client_name")
              )
              .map((headerInput, index) => {
                if (headerInput.title === "Cliente") {
                  return (
                    <Col sm={24} md={24} lg={12} key={index}>
                      <Row>
                        <Title level={5} style={{ fontWeight: "400" }}>
                          {headerInput.title}
                        </Title>
                      </Row>
                      <Row>
                        <Col span={23}>
                          <Form.Item
                            name={headerInput.formName}
                            initialValue={headerInput.value}
                            rules={[
                              {
                                required: true,
                                message: "Por favor, preencha o campo",
                              },
                            ]}
                          >
                            <Select
                              loading={allCustomers?.length <= 0 ? true : false}
                              showSearch
                              placeholder={headerInput.placeholder}
                              optionFilterProp="children"
                              filterOption={(input, option) =>
                                option?.children
                                  ?.toLowerCase()
                                  .startsWith(input?.toLowerCase()) ||
                                option?.children
                                  ?.toLowerCase()
                                  .includes(input?.toLowerCase())
                              }
                              filterSort={(optionA, optionB) =>
                                optionA?.children
                                  ?.toLowerCase()
                                  .localeCompare(
                                    optionB?.children?.toLowerCase()
                                  )
                              }
                              onSelect={handleSelectCustomer}
                            >
                              {allCustomers?.map((c, key) => {
                                const customerName = getCustomerNameOption(c);
                                if (customerName === "") {
                                  return (
                                    <Option key={key} value={"N/A"}>
                                      {"N/A"}
                                    </Option>
                                  );
                                } else {
                                  return (
                                    <Option key={key} value={customerName}>
                                      {customerName}
                                    </Option>
                                  );
                                }
                              })}
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                  );
                }

                return (
                  <Col sm={24} md={24} lg={12} key={index}>
                    <Row>
                      <Title level={5} style={{ fontWeight: "400" }}>
                        {headerInput.title}
                      </Title>
                    </Row>
                    <Row>
                      <Col span={23}>
                        {headerInput.inputType === "Input" ? (
                          <Form.Item
                            name={headerInput.formName}
                            initialValue={headerInput.value}
                            rules={[
                              {
                                required: true,
                                message: "Por favor, preencha o campo",
                              },
                            ]}
                          >
                            <Input
                              onChange={(e) => {
                                localStorage.setItem(
                                  headerInput.formName,
                                  e.target.value
                                );
                                setRecognizingHeaderChange(
                                  !recognizingHeaderChange
                                );
                              }}
                              placeholder={headerInput.placeholder}
                              style={{ width: "100%" }}
                            ></Input>
                          </Form.Item>
                        ) : (
                          <Form.Item
                            name={headerInput.formName}
                            rules={[
                              {
                                required: true,
                                message: "Por favor, preencha o campo",
                              },
                            ]}
                          >
                            <Select
                              onSelect={(e) => {
                                localStorage.setItem(headerInput.formName, e);
                              }}
                              onChange={() =>
                                setRecognizingHeaderChange(
                                  !recognizingHeaderChange
                                )
                              }
                              showSearch
                              placeholder={headerInput.placeholder}
                              optionFilterProp="children"
                              filterOption={(input, option) =>
                                option.children.includes(input)
                              }
                              filterSort={(optionA, optionB) =>
                                optionA.children
                                  .toLowerCase()
                                  .localeCompare(optionB.children.toLowerCase())
                              }
                            ></Select>
                          </Form.Item>
                        )}
                      </Col>
                    </Row>
                  </Col>
                );
              })}
          </Row>

          <Row style={{ marginBottom: "15px" }} gutter={[4, 0]}>
            {headerInputs
              .filter(
                (input) =>
                  !input.formName.includes("project_name") &&
                  !input.formName.includes("client_name")
              )
              .map((headerInput, index) => {
                if (headerInput.title === "Tipo") {
                  return (
                    <Col sm={24} md={24} lg={12} key={index}>
                      <Row>
                        <Title level={5} style={{ fontWeight: "400" }}>
                          {headerInput.title}
                        </Title>
                      </Row>
                      <Row>
                        <Col span={23}>
                          <Form.Item
                            name={headerInput.formName}
                            initialValue={headerInput.value}
                            rules={[
                              {
                                required: true,
                                message: "Por favor, preencha o campo",
                              },
                            ]}
                          >
                            <Select
                              id="blablabla"
                              loading={types?.length <= 0 ? true : false}
                              showSearch
                              placeholder={headerInput.placeholder}
                              optionFilterProp="children"
                              filterOption={(input, option) =>
                                option?.children
                                  ?.toLowerCase()
                                  .includes(input?.toLowerCase())
                              }
                              filterSort={(optionA, optionB) =>
                                optionA.children
                                  .toLowerCase()
                                  .localeCompare(optionB.children.toLowerCase())
                              }
                              onSelect={(e) => setSelectedType(e.target)}
                            >
                              {types.map((t) => (
                                <Option value={t}>{t}</Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                  );
                }
                if (
                  headerInput.title === "Status" ||
                  headerInput.title === "Status Comercial"
                ) {
                  return (
                    <Col sm={24} md={24} lg={12} key={index}>
                      <Row>
                        <Title level={5} style={{ fontWeight: "400" }}>
                          {headerInput.title}
                        </Title>
                      </Row>
                      <Row>
                        <Col span={23}>
                          <Form.Item
                            name={headerInput.formName}
                            initialValue={headerInput.value}
                            rules={[
                              {
                                required: true,
                                message: "Por favor, preencha o campo",
                              },
                            ]}
                          >
                            <Select
                              loading={status?.length <= 0 ? true : false}
                              showSearch
                              placeholder={headerInput.placeholder}
                              optionFilterProp="children"
                              filterOption={(input, option) =>
                                option?.children
                                  ?.toLowerCase()
                                  .includes(input?.toLowerCase())
                              }
                              filterSort={(optionA, optionB) =>
                                optionA.children
                                  .toLowerCase()
                                  .localeCompare(optionB.children.toLowerCase())
                              }
                              onSelect={(e) => {
                                setProposalState({
                                  type: TotalValuesProposalType.SET_STATUS,
                                  value: e,
                                });
                              }}
                            >
                              {status.map((t) => (
                                <Option value={t}>{t}</Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                  );
                }
              })}
          </Row>
        </Col>
        <Col span={6}>
          <SelectImage image={customerImage} onChangeImage={onChangeImage} />
        </Col>
      </Row>

      <Row
        justify="space-between"
        align="middle"
        style={{ marginBottom: "2rem" }}
        gutter={[12, 0]}
      >
        {headerInputsWithList.map((headerInputWithList, index) => {
          if (headerInputWithList.title === "Contatos") {
            return (
              <Col sm={24} md={12} lg={6}>
                <Row>
                  <Title level={5} style={{ fontWeight: "400" }}>
                    {headerInputWithList.title}
                  </Title>
                </Row>

                <Row>
                  <Col span={20}>
                    <Form.Item
                      initialValue={headerInputWithList.value || null}
                      name={headerInputWithList.formName}
                      rules={[
                        {
                          required: true,
                          message: "Por favor, preencha o campo",
                        },
                      ]}
                    >
                      <Select
                        showSearch
                        onSelect={(e) => {
                          localStorage.setItem(
                            "technical-proposal/main-contact",
                            e
                          );
                        }}
                        placeholder={headerInputWithList.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.children
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA.children
                            .toLowerCase()
                            .localeCompare(optionB.children.toLowerCase())
                        }
                      >
                        {selectedCustomer
                          ? selectedCustomer.contacts?.map((c, index) => {
                              return (
                                <Option value={`${index}`}>
                                  {c.names?.first_name}
                                </Option>
                              );
                            })
                          : null}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    {selectedCustomer ? (
                      <AddContact
                        contactsParent={contacts}
                        setContacts={(contacts) => setContacts(contacts)}
                      />
                    ) : (
                      <AddContact
                        contactsParent={null}
                        setContacts={(contacts) => setContacts(contacts)}
                      />
                    )}
                  </Col>
                </Row>
              </Col>
            );
          }

          if (headerInputWithList.title === "Arquitetos") {
            return (
              <Col sm={24} md={12} lg={6} key={index}>
                <Row>
                  <Title level={5} style={{ fontWeight: "400" }}>
                    {headerInputWithList.title}
                  </Title>
                </Row>
                <Row>
                  <Col span={24}>
                    <Form.Item
                      name={headerInputWithList.formName}
                      initialValue={headerInputWithList.value}
                      rules={[
                        {
                          required: true,
                          message: "Por favor, preencha o campo",
                        },
                      ]}
                    >
                      <Select
                        loading={architects?.length <= 0 ? true : false}
                        mode="multiple"
                        showSearch
                        placeholder={headerInputWithList.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.children
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA?.children
                            ?.toLowerCase()
                            .localeCompare(optionB.children?.toLowerCase())
                        }
                        onSelect={(e) => {
                          let prev = architects;
                          prev = [...prev, e.target];
                          setArchitects(prev);
                        }}
                      >
                        {architects.map((t) => (
                          <Option value={t}>{t}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
            );
          }

          if (headerInputWithList.title === "BUs") {
            return (
              <Col sm={24} md={12} lg={6} key={index}>
                <Row>
                  <Title level={5} style={{ fontWeight: "400" }}>
                    {headerInputWithList.title}
                  </Title>
                </Row>
                <Row>
                  <Col span={24}>
                    <Form.Item
                      name={headerInputWithList.formName}
                      initialValue={headerInputWithList.value}
                    >
                      <Select
                        loading={bus?.length <= 0 ? true : false}
                        mode="multiple"
                        showSearch
                        placeholder={headerInputWithList.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.children
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA?.children
                            ?.toLowerCase()
                            .localeCompare(optionB.children?.toLowerCase())
                        }
                        onSelect={(e) => {
                          let prev = bus;
                          prev = [...prev, e.target];
                          setBus(prev);
                        }}
                      >
                        {bus.map((t) => (
                          <Option value={t}>{t}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
            );
          }

          if (headerInputWithList.title === "Oportunidade") {
            return (
              <Col sm={24} md={12} lg={6} key={index}>
                <Row>
                  <Title level={5} style={{ fontWeight: "400" }}>
                    {headerInputWithList.title}
                  </Title>
                </Row>
                <Row>
                  <Col span={20}>
                    <Form.Item
                      name={headerInputWithList.formName}
                      initialValue={headerInputWithList.value}
                      rules={[
                        {
                          required: true,
                          message: "Por favor, preencha o campo",
                        },
                      ]}
                    >
                      <Select
                        showSearch
                        placeholder={headerInputWithList.placeholder}
                        optionFilterProp="children"
                        defaultOpen={[state?.mainOportunity]}
                        filterOption={(input, option) =>
                          option?.children.includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA?.children - optionB?.children
                        }
                        onSelect={(e) => {
                          setProposalState({
                            type: TotalValuesProposalType.SET_SELECTED_OPORTUNITY,
                            value: e,
                          });
                        }}
                      >
                        {oportunities.map((t) => (
                          <Option value={t}>{t}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <AddOpportunity
                      oportunitiesParent={oportunities}
                      setOportunities={(oportunities) => {
                        setOportunities(oportunities);
                      }}
                    />
                  </Col>
                </Row>
              </Col>
            );
          }

          return (
            <Col sm={24} md={12} lg={6}>
              <Row>
                <Title level={5} style={{ fontWeight: "400" }}>
                  {headerInputWithList.title}
                </Title>
              </Row>
              <Row>
                <Col span={20}>
                  <Form.Item
                    name={headerInputWithList.formName}
                    rules={[
                      {
                        required: true,
                        message: "Por favor, preencha o campo",
                      },
                    ]}
                  >
                    <Select
                      placeholder={headerInputWithList.placeholder}
                      style={{ width: "100%" }}
                    ></Select>
                  </Form.Item>
                </Col>
                <Col span={4}>{headerInputWithList.component}</Col>
              </Row>
            </Col>
          );
        })}
      </Row>
    </Col>
  );
};
