#!/usr/bin/env node

/**
 * Script para testar as correções de autenticação
 * Verifica se o backend e frontend estão funcionando corretamente
 */

const axios = require('axios');
const chalk = require('chalk');

// Configurações
const API_BASE = 'https://api.dsm.darede.com.br/dev';
const FRONTEND_URL = 'http://localhost:3000';

console.log(chalk.blue.bold('🔧 TESTE DAS CORREÇÕES DE AUTENTICAÇÃO'));
console.log(chalk.blue('=' .repeat(50)));

/**
 * Testar conectividade básica com a API
 */
async function testApiConnectivity() {
  console.log(chalk.yellow('\n📡 Testando conectividade com a API...'));
  
  try {
    // Teste básico de conectividade
    const response = await axios.get(`${API_BASE}/health`, {
      timeout: 10000,
      validateStatus: () => true // Aceitar qualquer status
    });
    
    console.log(chalk.green(`✅ API acessível - Status: ${response.status}`));
    return true;
  } catch (error) {
    console.log(chalk.red(`❌ API inacessível: ${error.message}`));
    return false;
  }
}

/**
 * Testar endpoint de verificação de autenticação
 */
async function testAuthVerify() {
  console.log(chalk.yellow('\n🔐 Testando endpoint /auth/verify...'));
  
  try {
    const response = await axios.get(`${API_BASE}/auth/verify`, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 400 || response.status === 401) {
      console.log(chalk.green(`✅ Endpoint /auth/verify funcionando (${response.status} - sem token)`));
      return true;
    } else {
      console.log(chalk.yellow(`⚠️ Endpoint /auth/verify retornou: ${response.status}`));
      return false;
    }
  } catch (error) {
    console.log(chalk.red(`❌ Erro no /auth/verify: ${error.message}`));
    return false;
  }
}

/**
 * Testar endpoint /cognito/read (deve retornar 401 sem autenticação)
 */
async function testCognitoRead() {
  console.log(chalk.yellow('\n👥 Testando endpoint /cognito/read...'));
  
  try {
    const response = await axios.get(`${API_BASE}/cognito/read`, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 401) {
      console.log(chalk.green(`✅ Endpoint /cognito/read protegido corretamente (401)`));
      return true;
    } else if (response.status === 500) {
      console.log(chalk.red(`❌ Erro 500 no /cognito/read - Problema no backend`));
      console.log(chalk.red(`   Dados: ${JSON.stringify(response.data, null, 2)}`));
      return false;
    } else {
      console.log(chalk.yellow(`⚠️ Endpoint /cognito/read retornou: ${response.status}`));
      return false;
    }
  } catch (error) {
    console.log(chalk.red(`❌ Erro no /cognito/read: ${error.message}`));
    return false;
  }
}

/**
 * Testar configurações de CORS
 */
async function testCorsConfig() {
  console.log(chalk.yellow('\n🌐 Testando configurações de CORS...'));
  
  try {
    const response = await axios.options(`${API_BASE}/auth/verify`, {
      headers: {
        'Origin': FRONTEND_URL,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    const corsHeaders = {
      'access-control-allow-origin': response.headers['access-control-allow-origin'],
      'access-control-allow-credentials': response.headers['access-control-allow-credentials'],
      'access-control-allow-methods': response.headers['access-control-allow-methods'],
      'access-control-allow-headers': response.headers['access-control-allow-headers']
    };
    
    console.log(chalk.green('✅ Headers CORS encontrados:'));
    Object.entries(corsHeaders).forEach(([key, value]) => {
      if (value) {
        console.log(chalk.cyan(`   ${key}: ${value}`));
      }
    });
    
    const allowsCredentials = corsHeaders['access-control-allow-credentials'] === 'true';
    const allowsOrigin = corsHeaders['access-control-allow-origin'] === FRONTEND_URL || 
                        corsHeaders['access-control-allow-origin'] === '*';
    
    if (allowsCredentials && allowsOrigin) {
      console.log(chalk.green('✅ CORS configurado corretamente para cookies HttpOnly'));
      return true;
    } else {
      console.log(chalk.yellow('⚠️ CORS pode ter problemas com cookies HttpOnly'));
      return false;
    }
  } catch (error) {
    console.log(chalk.red(`❌ Erro no teste de CORS: ${error.message}`));
    return false;
  }
}

/**
 * Verificar variáveis de ambiente do backend
 */
async function checkBackendConfig() {
  console.log(chalk.yellow('\n⚙️ Verificando configurações do backend...'));
  
  const requiredEnvVars = [
    'USER_POOL_ID',
    'AWS_REGION_LOCATION',
    'ALLOWED_ORIGINS',
    'JWT_DECRIPTION_CREDENTIALS'
  ];
  
  console.log(chalk.cyan('Variáveis necessárias no backend:'));
  requiredEnvVars.forEach(envVar => {
    console.log(chalk.cyan(`   - ${envVar}`));
  });
  
  console.log(chalk.green('✅ Lista de verificação criada'));
  return true;
}

/**
 * Executar todos os testes
 */
async function runAllTests() {
  console.log(chalk.blue('\n🚀 Executando todos os testes...\n'));
  
  const tests = [
    { name: 'Conectividade da API', fn: testApiConnectivity },
    { name: 'Endpoint /auth/verify', fn: testAuthVerify },
    { name: 'Endpoint /cognito/read', fn: testCognitoRead },
    { name: 'Configurações CORS', fn: testCorsConfig },
    { name: 'Configurações Backend', fn: checkBackendConfig }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
    } catch (error) {
      console.log(chalk.red(`❌ Erro no teste "${test.name}": ${error.message}`));
      results.push({ name: test.name, success: false });
    }
  }
  
  // Resumo
  console.log(chalk.blue('\n📊 RESUMO DOS TESTES'));
  console.log(chalk.blue('=' .repeat(30)));
  
  let allPassed = true;
  results.forEach(result => {
    const icon = result.success ? '✅' : '❌';
    const color = result.success ? chalk.green : chalk.red;
    console.log(color(`${icon} ${result.name}`));
    if (!result.success) allPassed = false;
  });
  
  console.log(chalk.blue('\n' + '=' .repeat(50)));
  
  if (allPassed) {
    console.log(chalk.green.bold('🎉 TODOS OS TESTES PASSARAM!'));
    console.log(chalk.green('✅ Sistema pronto para uso'));
  } else {
    console.log(chalk.yellow.bold('⚠️ ALGUNS TESTES FALHARAM'));
    console.log(chalk.yellow('🔧 Verificar configurações e logs'));
  }
  
  console.log(chalk.blue('\n📋 PRÓXIMOS PASSOS:'));
  console.log(chalk.cyan('1. Iniciar o frontend: npm start'));
  console.log(chalk.cyan('2. Acessar: http://localhost:3000'));
  console.log(chalk.cyan('3. Fazer login via Cognito'));
  console.log(chalk.cyan('4. Verificar painel de debug na Home (desenvolvimento)'));
  console.log(chalk.cyan('5. Testar acesso aos dados do Cognito'));
  
  return allPassed;
}

// Executar testes se chamado diretamente
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error(chalk.red('💥 Erro fatal:', error.message));
      process.exit(1);
    });
}

module.exports = {
  testApiConnectivity,
  testAuthVerify,
  testCognitoRead,
  testCorsConfig,
  checkBackendConfig,
  runAllTests
};
