/**
 * Rate Limiter Middleware
 * Implementa controle de taxa de requisições para prevenir abuso da API
 */

class RateLimiter {
  constructor(options = {}) {
    this.windowMs = options.windowMs || 15 * 60 * 1000; // 15 minutos por padrão
    this.maxRequests = options.maxRequests || 100; // 100 requisições por padrão
    this.keyGenerator = options.keyGenerator || this.defaultKeyGenerator;
    this.onLimitReached = options.onLimitReached || this.defaultOnLimitReached;
    this.skipSuccessfulRequests = options.skipSuccessfulRequests || false;
    this.skipFailedRequests = options.skipFailedRequests || false;
    
    // Armazenamento em memória (em produção, usar Redis)
    this.store = new Map();
    
    // Limpeza periódica do store
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, this.windowMs);
  }

  /**
   * Gerador de chave padrão baseado no IP
   */
  defaultKeyGenerator(req) {
    return req.ip || req.connection.remoteAddress || 'unknown';
  }

  /**
   * Callback padrão quando o limite é atingido
   */
  defaultOnLimitReached(req, res) {
    res.status(429).json({
      error: 'Too Many Requests',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: Math.ceil(this.windowMs / 1000)
    });
  }

  /**
   * Middleware principal do rate limiter
   */
  middleware() {
    return (req, res, next) => {
      const key = this.keyGenerator(req);
      const now = Date.now();
      
      // Obter ou criar entrada para a chave
      let entry = this.store.get(key);
      if (!entry) {
        entry = {
          count: 0,
          resetTime: now + this.windowMs,
          requests: []
        };
        this.store.set(key, entry);
      }

      // Verificar se a janela de tempo expirou
      if (now > entry.resetTime) {
        entry.count = 0;
        entry.resetTime = now + this.windowMs;
        entry.requests = [];
      }

      // Verificar se o limite foi excedido
      if (entry.count >= this.maxRequests) {
        this.onLimitReached(req, res);
        return;
      }

      // Incrementar contador
      entry.count++;
      entry.requests.push({
        timestamp: now,
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent')
      });

      // Adicionar headers informativos
      res.set({
        'X-RateLimit-Limit': this.maxRequests,
        'X-RateLimit-Remaining': Math.max(0, this.maxRequests - entry.count),
        'X-RateLimit-Reset': new Date(entry.resetTime).toISOString(),
        'X-RateLimit-Window': this.windowMs
      });

      // Interceptar resposta para controle condicional
      const originalSend = res.send;
      res.send = function(data) {
        const statusCode = res.statusCode;
        
        // Se configurado para pular requisições bem-sucedidas
        if (this.skipSuccessfulRequests && statusCode < 400) {
          entry.count--;
        }
        
        // Se configurado para pular requisições falhadas
        if (this.skipFailedRequests && statusCode >= 400) {
          entry.count--;
        }
        
        return originalSend.call(this, data);
      }.bind(this);

      next();
    };
  }

  /**
   * Limpeza de entradas expiradas
   */
  cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.store.entries()) {
      if (now > entry.resetTime) {
        this.store.delete(key);
      }
    }
  }

  /**
   * Obter estatísticas do rate limiter
   */
  getStats() {
    const stats = {
      totalKeys: this.store.size,
      entries: []
    };

    for (const [key, entry] of this.store.entries()) {
      stats.entries.push({
        key,
        count: entry.count,
        resetTime: new Date(entry.resetTime).toISOString(),
        requests: entry.requests.length
      });
    }

    return stats;
  }

  /**
   * Reset manual de uma chave específica
   */
  reset(key) {
    this.store.delete(key);
  }

  /**
   * Reset completo do rate limiter
   */
  resetAll() {
    this.store.clear();
  }

  /**
   * Destruir o rate limiter
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.store.clear();
  }
}

/**
 * Configurações pré-definidas para diferentes cenários
 */
export const rateLimitConfigs = {
  // Configuração estrita para APIs sensíveis
  strict: {
    windowMs: 15 * 60 * 1000, // 15 minutos
    maxRequests: 50,
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  },

  // Configuração moderada para uso geral
  moderate: {
    windowMs: 15 * 60 * 1000, // 15 minutos
    maxRequests: 100,
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  },

  // Configuração permissiva para desenvolvimento
  permissive: {
    windowMs: 15 * 60 * 1000, // 15 minutos
    maxRequests: 1000,
    skipSuccessfulRequests: true,
    skipFailedRequests: true
  },

  // Configuração para autenticação
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutos
    maxRequests: 5, // Apenas 5 tentativas de login
    skipSuccessfulRequests: true,
    skipFailedRequests: false
  }
};

/**
 * Factory function para criar rate limiters
 */
export function createRateLimiter(config = 'moderate') {
  const options = typeof config === 'string' ? rateLimitConfigs[config] : config;
  return new RateLimiter(options);
}

/**
 * Rate limiter baseado em usuário autenticado
 */
export function createUserRateLimiter(options = {}) {
  return new RateLimiter({
    ...options,
    keyGenerator: (req) => {
      // Usar ID do usuário se autenticado, senão usar IP
      return req.user?.id || req.ip || 'anonymous';
    }
  });
}

/**
 * Rate limiter para endpoints específicos
 */
export function createEndpointRateLimiter(endpoint, options = {}) {
  return new RateLimiter({
    ...options,
    keyGenerator: (req) => {
      const baseKey = req.user?.id || req.ip || 'anonymous';
      return `${baseKey}:${endpoint}`;
    }
  });
}

export default RateLimiter;
