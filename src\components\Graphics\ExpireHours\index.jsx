import { React, useCallback, useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Sector, Legend } from "recharts";
import { Popover, Modal, Spin, Typography, Card, Col, Row } from "antd";
import {
  BgColorsOutlined,
  FilterOutlined,
  StarFilled,
  StarOutlined,
} from "@ant-design/icons";
import TableExpire from "../../Table/ExpireHours";
import ColorPicker from "../../ColorPicker";
import "./ExpireHours.css";
import { otrsGet } from "../../../service/apiOtrs";
import moment from "moment";
import { MainDonutChart } from "../../Graphs/DonutGraph";

const renderActiveShape = (props) => {
  const {
    cx,
    cy,
    innerRadius,
    outerRadius,
    startAngle,
    endAngle,
    fill,
    payload,
  } = props;

  return (
    <g>
      <text x={cx} y={cy + 10} dy={-5} textAnchor="middle" fill={"black"}>
        {payload.value} Horas
      </text>

      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
      />
    </g>
  );
};

export default function ExpireHours(props) {
  const { Title, Text } = Typography;
  const [activeIndex, setActiveIndex] = useState(0);
  const [pieGraphCurrentData, setPieGraphCurrentData] = useState();
  const [isFavorite, setIsFavorite] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [colors, setColors] = useState();
  const [loading, setLoading] = useState(true);
  const [pieChartData, setPieChartData] = useState([]);

  const GRAPH_NAME = "ExpireHours";

  function showModal() {
    setModalVisible(!modalVisible);
  }

  function handleOk() {
    setModalVisible(false);
  }

  function handleCancel() {
    setModalVisible(false);
  }

  useEffect(() => {
    getGraphColor();
    getContracts();
  }, []);
  
  useEffect(() => {
    setIsFavorite(checkIsVisible());
  },[props.favorites])

  function getGraphColor() {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (storage_color) {
      setColors(storage_color[GRAPH_NAME]);
    }
  }

  function changeColors(colors) {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (!storage_color) {
      storage_color = {};
    }
    storage_color[GRAPH_NAME] = colors;
    localStorage.setItem("colors", JSON.stringify(storage_color));
    setColors(colors);
  }

  async function getContracts() {
    const { data } = await otrsGet("read/contract/all/0");
    const today = moment();

    const payload = [
      {
        name: "Expiradas",
        value: 0,
        fill: colors ? colors[1].color : "red",
        contracts: [],
      },
      {
        name: "Expira no mês atual",
        value: 0,
        fill: colors ? colors[3].color : "blue",
        contracts: [],
      },
      {
        name: "Expira em um mês",
        value: 0,
        fill: colors ? colors[4].color : "orange",
        contracts: [],
      },
      {
        name: "Expira em 2 meses",
        value: 0,
        fill: colors ? colors[2].color : "#43914F",
        contracts: [],
      },
    ];

    Object.entries(data).forEach((item) => {
      const contract = item[1];

      const contractEndDate = contract.end_date
        ? moment(contract.end_date)
        : false;
      const contractReservedHours =
        parseFloat(contract.total_hours) -
        parseFloat(contract.cumulative_hours);

      if (contractEndDate && contract.valid_id === 1) {
        if (today >= contractEndDate) {
          payload[0].value +=
            contractReservedHours > 0 ? contractReservedHours : 0;
          payload[0].contracts.push(contract);
        } else {
          if (
            contractEndDate.month() === today.month() &&
            contractEndDate.year() === today.year()
          ) {
            payload[1].value +=
              contractReservedHours > 0 ? contractReservedHours : 0;
            payload[1].contracts.push(contract);
          } else {
            const monthDiff = contractEndDate.diff(today, "months", true);
            if (monthDiff <= 1) {
              payload[2].value +=
                contractReservedHours > 0 ? contractReservedHours : 0;
              payload[2].contracts.push(contract);
            } else if (monthDiff <= 2) {
              payload[3].value +=
                contractReservedHours > 0 ? contractReservedHours : 0;
              payload[3].contracts.push(contract);
            }
          }
        }
      }
    });
    setPieChartData(payload);
    setLoading(false);
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );

      return index !== -1;
    } else {
      return false;
    }
  }

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  const onPieEnter = useCallback(
    (e, index) => {
      setPieGraphCurrentData(e);
      setActiveIndex(index);
    },
    [setActiveIndex]
  );

  function swapVisible() {
    let object = structuredClone(props.componentVisibles);
    object.expireTable = !object.expireTable;
    props.setComponentVisibles(object);
  }

  return (
    <Card bordered="false" style={{ borderRadius: "20px", height: "100%" }}>
      {loading ? (
        <Row justify="center" align="middle" style={{ height: "250px" }}>
          <Col style={{ display: "flex", flexDirection: "column" }}>
            <Spin />
            <Text>Carregando...</Text>
          </Col>
        </Row>
      ) : (
        <>
          <Row justify="space-between">
            <Col span={18}>
              <Title level={4} style={{ fontWeight: 400 }}>
                Horas a expirar e expiradas
              </Title>
            </Col>
            <Col span={3}>
              {!isFavorite ? (
                <StarOutlined onClick={updateFavorite} className="star-icon" />
              ) : (
                <StarFilled onClick={updateFavorite} className="filter-icon" />
              )}
            </Col>
          </Row>
          <MainDonutChart
            data={pieChartData}
            modalState={props?.isModal}
            swapVisible={swapVisible}
            showModalFunction={showModal}
            dataKey="value"
            onPieEnter={onPieEnter}
            activeIndex={activeIndex}
            setActiveIndex={setActiveIndex}
            pieGraphCurrentData={pieGraphCurrentData}
            renderActiveShape={renderActiveShape}
          />
          <Modal
            closable={true}
            width={"85vw"}
            footer={null}
            open={modalVisible}
            onOk={handleOk}
            onCancel={handleCancel}
          >
            <TableExpire
              showModal={showModal}
              data={pieChartData[activeIndex]}
            />
          </Modal>
        </>
      )}
    </Card>
  );
}
