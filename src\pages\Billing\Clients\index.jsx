import { useState } from 'react'
import { SideMenu } from '../../../components/SideMenu'
import { HeaderMenu } from '../../../components/HeaderMenu'
import {
  Layout,
  Card,
  Table,
  Row,
  Col,
  Input,
  Space,
  Typography,
  Select,
  Tag,
  Popconfirm,
  message,
  Button
} from 'antd'
import { ClientUsersModal } from '../../../components/Modals/Clients/ClientUsersModal'
import { ClientContractsModal } from '../../../components/Modals/Clients/ClientContractsModal'
import { ClientAccountsModal } from '../../../components/Modals/Clients/ClientAccountsModal'
import { ClientEditModal } from '../../../components/Modals/Clients/ClientEdit'
import { ClientCreateModal } from '../../../components/Modals/Clients/ClientCreate'
import {
  dynamoGetById,
  dynamoPut,
  useDynamoGet
} from '../../../service/apiDsmDynamo'
import { otrsPut } from '../../../service/apiOtrs'
import useSWR from 'swr'
import { VisualizeInfo } from '../../../components/Modals/Clients/VisualizeInfo'
import { format, isValid } from 'date-fns'

const { Content } = Layout
const { Text } = Typography
const { Option } = Select

export const Clients = () => {
  const permissions = useSWR('clients', async () => {
    let data = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-permissions`,
      localStorage.getItem('@dsm/permission')
    )

    return [...data.permissions.find(x => x.page === 'Clientes').actions]
  })

  const { data, mutate } = useDynamoGet(
    `${process.env.REACT_APP_STAGE}-customers`
  )
  const contracts = useDynamoGet(`${process.env.REACT_APP_STAGE}-contracts`)
  const [collapsed, setCollapsed] = useState(false)
  const [loading, setLoading] = useState(false)
  const [state, setState] = useState('ativosA')
  const [search, setSearch] = useState('')

  if (!data && !loading) {
    return setLoading(true)
  }

  if (data && loading) {
    return setLoading(false)
  }

  function filterContracts(contracts, customer, state) {
    switch (state) {
      case 'all':
        return contracts?.filter(
          contract =>
            (contract?.customer?.id || contract.customer?.itsm_id) &&
            (contract?.customer?.id?.toString() === customer?.id?.toString() ||
              contract.customer?.itsm_id?.toString() ===
                customer.identifications?.itsm_id?.toString())
        )

      case 'ativosA':
        return contracts?.filter(
          (contract, index, array) =>
            (contract?.customer?.id || contract.customer?.itsm_id) &&
            (contract?.customer?.id?.toString() === customer?.id?.toString() ||
              contract.customer?.itsm_id?.toString() ===
                customer.identifications?.itsm_id?.toString()) &&
            array
              .filter(
                contract =>
                  (contract?.customer?.id || contract.customer?.itsm_id) &&
                  (contract?.customer?.id?.toString() ===
                    customer?.id?.toString() ||
                    contract.customer?.itsm_id?.toString() ===
                      customer.identifications?.itsm_id?.toString())
              )
              .some(x => x.active)
        )

      case 'ativosI':
        return contracts?.filter(
          (contract, index, array) =>
            (contract?.customer?.id || contract.customer?.itsm_id) &&
            (contract?.customer?.id?.toString() === customer?.id?.toString() ||
              contract.customer?.itsm_id?.toString() ===
                customer.identifications?.itsm_id?.toString()) &&
            array
              .filter(
                contract =>
                  (contract?.customer?.id || contract.customer?.itsm_id) &&
                  (contract?.customer?.id?.toString() ===
                    customer?.id?.toString() ||
                    contract.customer?.itsm_id?.toString() ===
                      customer.identifications?.itsm_id?.toString())
              )
              .every(x => !x.active)
        )

      case 'prospect':
        return !contracts?.filter(
          (contract, index, array) =>
            (contract?.customer?.id || contract.customer?.itsm_id) &&
            (contract?.customer?.id?.toString() === customer?.id?.toString() ||
              contract.customer?.itsm_id?.toString() ===
                customer.identifications?.itsm_id?.toString())
        ).length

      default:
        break
    }
  }

  const changeActive = async client => {
    setLoading(true)
    try {
      try {
        await otrsPut('update/customer/' + client.identifications.itsm_id, {
          name: client?.names?.fantasy_name,
          razaosocial: client?.names?.name,
          street: `${client?.address?.tipo_logradouro} ${
            client?.address?.logradouro
          }, ${client?.address?.bairro}${
            client?.address?.complemento
              ? ' ' + client?.address?.complemento
              : ''
          }, ${client?.address?.numero}`,
          country: client?.address?.pais,
          city: client?.address?.cidade,
          zip: client?.address?.cep,
          cnpj: client?.cnpj,
          valid_id: client?.active === 1 ? 2 : 1
        })
      } catch (error) {
        setLoading(false)
        console.log(error)
        return message.error(
          'Ocorreu um erro ao tentar atualizar o cliente no OTRS :('
        )
      }

      try {
        await dynamoPut(`${process.env.REACT_APP_STAGE}-customers`, client.id, {
          active: client?.active ? 0 : 1
        })
        mutate(
          data.map(c => {
            if (c.id === client.id) {
              return { ...c, active: client?.active ? 0 : 1 }
            }

            return c
          }),
          false
        )
      } catch (error) {
        setLoading(false)
        console.log(error)
        return message.error(
          'O cliente foi atualizado no OTRS, mas ocorreu um erro ao tentar atualizar o cliente no DSM :('
        )
      }

      message.success(
        `Sucesso na ${
          client.active === 1 ? 'desativação' : 'ativação'
        } deste cliente...`
      )
    } catch (err) {
      console.log(err)
      message.error('Ops! Ocorreu um erro ao tentar realizar esta ação...')
      setLoading(false)
    }
  }

  const columns = [
    {
      code: 'view_date',
      title: 'Data de Criação',
      dataIndex: 'created_at',
      align: 'center',
      render: date =>
        isValid(new Date(date))
          ? format(new Date(date), 'dd/MM/yyyy')
          : 'Data inválida',
      defaultSortOrder: 'descend',
      sortDirections: ['descend', 'ascend'],
      sorter: (a, b) => new Date(a?.created_at) - new Date(b?.created_at),
      width: '155px'
    },
    {
      code: 'view_crm',
      title: 'CRM',
      key: 'id',
      align: 'center',
      dataIndex: ['identifications', 'crm_id'],
      sorter: (a, b) =>
        parseInt(a.identifications?.crm_id) -
        parseInt(b.identifications?.crm_id),
      sortDirections: ['descend', 'ascend'],
      width: '1%'
    },
    {
      code: 'view_itsm',
      title: 'ITSM',
      align: 'center',
      dataIndex: ['identifications', 'itsm_id'],
      sorter: (a, b) =>
        parseInt(a.identifications?.itsm_id) -
        parseInt(b.identifications?.itsm_id),
      sortDirections: ['descend', 'ascend'],
      width: '1%'
    },
    {
      code: 'view_fantasy_name',
      title: 'Nome',
      dataIndex: ['names', 'fantasy_name']
    },
    {
      code: 'view_razao_social',
      title: 'Razão Social',
      dataIndex: ['names', 'name']
    },
    {
      code: 'view_cnpj',
      title: 'CNPJ',
      dataIndex: 'cnpj',
      key: 'cnpj'
    },
    {
      code: 'view_info',
      title: 'Informações',
      dataIndex: 'id',
      key: 'id',
      width: '1%',
      align: 'center',
      render: (field, item) => {
        return <VisualizeInfo client={item} />
      }
    },
    {
      code: 'view_contacts',
      title: 'Contatos',
      dataIndex: 'contacts',
      key: 'contacts',
      width: '1%',
      render: (field, item) => {
        return (
          <ClientUsersModal
            mutate={(data, options) => mutate(data, options)}
            contracts={filterContracts(contracts?.data, item, 'all')}
            permissions={permissions?.data}
            contacts={field}
            clients={data}
            client={item}
          />
        )
      }
    },
    {
      code: 'view_contracts',
      title: 'Contratos',
      dataIndex: 'id',
      key: 'id',
      width: '1%',
      render: (id, item) => {
        return (
          <ClientContractsModal
            mutate={(data, options) => contracts.mutate(data, options)}
            contracts={filterContracts(contracts?.data, item, 'all')}
            client={item}
            permissions={permissions}
          />
        )
      }
    },
    {
      code: 'view_accounts',
      title: 'Contas',
      dataIndex: 'accounts',
      key: 'accounts',
      width: '1%',
      render: (field, item) => {
        return (
          <ClientAccountsModal
            mutate={(data, options) => mutate(data, options)}
            permissions={permissions?.data}
            accounts={field}
            clients={data}
            client={item}
          />
        )
      }
    },
    {
      code: 'view_edit',
      title: 'Editar',
      dataIndex: 'id',
      key: 'id',
      width: '1%',
      render: (id, item) => {
        return (
          <ClientEditModal
            mutate={(data, option) => mutate(data, option)}
            clients={data}
            client={item}
            id={id}
          />
        )
      }
    },
    {
      code: 'view_actions',
      title: 'Ações',
      dataIndex: 'active',
      key: 'active',
      width: '1%',
      render: (active, item) => {
        return (
          <Row justify="center">
            {active === 1 ? (
              <Popconfirm
                placement="leftBottom"
                title="Tem certeza que deseja desativar este cliente?"
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  cursor: 'pointer',
                  color: 'black'
                }}
                onConfirm={() => changeActive(item)}
              >
                <Button style={{ padding: '0' }} type="text">
                  <Tag color="red">Desativar</Tag>
                </Button>
              </Popconfirm>
            ) : (
              <Popconfirm
                placement="leftBottom"
                title="Tem certeza que deseja ativar este cliente?"
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  cursor: 'pointer',
                  color: 'black'
                }}
                onConfirm={() => changeActive(item)}
              >
                <Button style={{ padding: '0' }} type="text">
                  <Tag color="green">Ativar</Tag>
                </Button>
              </Popconfirm>
            )}
          </Row>
        )
      }
    }
  ]

  const pagination = {
    data: []
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: '2em' }}>
          <Card
            style={{
              boxShadow: '0 0 10px rgba(0,0,0,0.1)',
              borderRadius: '20px'
            }}
          >
            <Row justify="space-between">
              <Col>
                <Space wrap>
                  <Input
                    onChange={e => setSearch(e.target.value)}
                    style={{
                      width: '300px',
                      height: '35px',
                      borderRadius: '7px'
                    }}
                    placeholder="Buscar cliente..."
                  />
                  {permissions?.data
                    ?.map(permission => {
                      return permission.code
                    })
                    .includes('create_client') ? (
                    <ClientCreateModal
                      clients={data}
                      mutate={(data, option) => mutate(data, option)}
                    />
                  ) : (
                    ''
                  )}
                </Space>
              </Col>
              <Col>
                <Space>
                  <Text>Filtrar por: </Text>
                  <Select
                    onChange={setState}
                    defaultValue="ativosA"
                    style={{ minWidth: '176px' }}
                  >
                    <Option value="todos">Todos</Option>
                    <Option value="prospect">Prospect</Option>
                    <Option value="ativosA">Ativos com Contrato Ativo</Option>
                    <Option value="ativosI">Ativos com Contrato Inativo</Option>
                    <Option value="inativosC">Inativos</Option>
                  </Select>
                </Space>
              </Col>
            </Row>
            <Table
              scroll={{ x: '100%' }}
              loading={loading}
              style={{ minWidth: '100%', marginTop: '20px' }}
              columns={columns.filter(e =>
                permissions?.data
                  ?.map(permission => {
                    return permission.code
                  })
                  .includes(e.code)
              )}
              dataSource={data?.filter(e => {
                const filter = [
                  e?.identifications?.itsm_id !== null &&
                  e?.identifications?.itsm_id !== ''
                    ? e?.identifications?.itsm_id
                        ?.toString()
                        ?.includes(search.toLowerCase())
                    : false,
                  e?.identifications?.itsm_id !== null &&
                  e?.identifications?.itsm_id !== ''
                    ? e?.identifications?.itsm_id
                        ?.toString()
                        ?.includes(search.toLowerCase())
                    : false,
                  e?.names?.name !== null && e?.names?.name !== ''
                    ? e?.names?.name
                        ?.toLowerCase()
                        ?.toString()
                        ?.includes(search.toLowerCase())
                    : false,
                  e?.names?.fantasy_name !== null &&
                  e?.names?.fantasy_name !== ''
                    ? e?.names?.fantasy_name
                        ?.toLowerCase()
                        ?.toString()
                        ?.includes(search.toLowerCase())
                    : false,
                  e?.cnpj !== null && e?.cnpj !== ''
                    ? e?.cnpj?.toString()?.includes(search.toLowerCase())
                    : false
                ]

                if (state === 'ativosA' || state === '') {
                  for (let i = 0; i < filter.length; i++) {
                    if (filter[i] === true) {
                      return (
                        e?.active === 1 &&
                        filterContracts(contracts?.data, e, 'ativosA')?.length
                      )
                    }
                  }
                } else if (state === 'inativosC') {
                  for (let i = 0; i < filter.length; i++) {
                    if (filter[i] === true) {
                      return e?.active === 0
                    }
                  }
                } else if (state === 'ativosI') {
                  for (let i = 0; i < filter.length; i++) {
                    if (filter[i] === true) {
                      return (
                        e?.active === 1 &&
                        filterContracts(contracts?.data, e, 'ativosI')?.length
                      )
                    }
                  }
                } else if (state === 'prospect') {
                  for (let i = 0; i < filter.length; i++) {
                    if (filter[i] === true) {
                      return (
                        e?.active === 1 &&
                        filterContracts(contracts?.data, e, 'prospect')
                      )
                    }
                  }
                } else if (state === 'todos') {
                  for (let i = 0; i < filter.length; i++) {
                    if (filter[i] === true) {
                      return e
                    }
                  }
                }

                if (search === '') return e

                return null
              })}
              pagination={pagination}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  )
}
