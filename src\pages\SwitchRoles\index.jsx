import { useEffect, useMemo, useState, useRef } from "react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import { Card, Layout } from "antd";
import { SolicitationsModal } from "../../components/Modals/SwitchRoles/SolicitationsModal";
import useSWR from "swr";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import * as permissionSetsController from "../PermissionSets/controllers/index";
import * as switchRoleController from "./controllers/index";
import { DynamicTable } from "../../components/Table/DynamicTable";
import {
  OTRSTicketNumberRedirector,
  SwitchRolesHeader,
} from "./components/index";
import { useSelector } from "react-redux";
import { filterTableData } from "../../utils/filterTableData";
import { Counter } from "../../components/Counter";

export const SwitchRoles = () => {
  const solicitations = useSelector((state) => state.switchRole.switchRoleData);
  const permissionSets = useSelector(
    (state) => state.switchRole.permissionSets
  );
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [state, setState] = useState("");
  const { Content } = Layout;

  // ✅ Ref para controlar se os dados já foram carregados
  const dataLoadedRef = useRef(false);

  const permissions = useSWR("access", async () => {
    let data = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-permissions`,
      localStorage.getItem("@dsm/permission")
    );

    return [...data.permissions.find((x) => x.page === "Switch Roles").actions];
  });

  const columns = [
    {
      code: "view_ticket",
      dataIndex: "client_ticket",
      title: "Ticket",
      align: "center",
      width: "100px",
      render: (ticket) => <OTRSTicketNumberRedirector ticket={ticket} />,
    },
    {
      code: "view_client",
      dataIndex: "client_name",
      title: "Nome do Cliente",
      sorter: (a, b) => a?.client_name?.localeCompare(b?.client_name),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_user",
      dataIndex: "username",
      title: "Usuário",
      sorter: (a, b) => a?.username?.localeCompare(b?.username),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_actions",
      dataIndex: "id",
      title: "Ações",
      width: "1%",
      render: (_, item) => (
        <SolicitationsModal
          solicitations={item.solicitations}
          permissions={permissions?.data}
          client={item}
        />
      ),
    },
  ];

  // ✅ Primeiro useEffect: carrega permission sets apenas uma vez
  useEffect(() => {
    async function loadPermissionSets() {
      if (dataLoadedRef.current) {
        console.log("⚠️ Permission sets já carregados, evitando recarregamento");
        return;
      }

      try {
        console.log("🔄 Carregando permission sets...");
        await permissionSetsController.getPermissionSets();
        console.log("✅ Permission sets carregados com sucesso");
        dataLoadedRef.current = true;
      } catch (error) {
        console.error("❌ Erro ao carregar permission sets:", error);
      }
    }

    loadPermissionSets();
  }, []); // ✅ Executa apenas uma vez

  // ✅ Segundo useEffect: carrega dados do switch role quando permission sets estão disponíveis
  useEffect(() => {
    async function loadSwitchRoleData() {
      if (!dataLoadedRef.current || !permissionSets || permissionSets.length === 0) {
        return; // Aguarda permission sets serem carregados
      }

      setLoading(true);
      try {
        console.log("🔄 Carregando dados do Switch Roles com permission sets...");
        await switchRoleController.getAllSwitchRoleData(permissionSets);
        console.log("✅ Dados do Switch Roles carregados com sucesso");
      } catch (error) {
        console.error("❌ Erro ao carregar dados do Switch Roles:", error);
      } finally {
        setLoading(false);
      }
    }

    loadSwitchRoleData();
  }, [permissionSets?.length]); // ✅ Reage apenas ao comprimento do array, não ao conteúdo

  const filterByState = (data, state) => {
    switch (state) {
      case "todos":
        return data;
      case "inactive":
        return data?.filter((e) => e.allowed === false);
      case "active":
        return data?.filter((e) => e.allowed === true);
      default:
        return data;
    }
  };

  const tableData = useMemo(() => {
    let filteredData = solicitations || [];

    if (search !== "") {
      const searchFields = ["client_name", "client_ticket", "username"];
      filteredData = filterTableData({
        data: filteredData,
        search,
        searchFields,
      });
    }

    filteredData = filterByState(filteredData, state);

    return filteredData;
  }, [solicitations, state, search]);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
            }}
          >
            <SwitchRolesHeader setSearch={setSearch} setState={setState} />
            <Counter tableData={tableData} />
            <DynamicTable
              scroll={{ x: "100%" }}
              dataSource={tableData}
              columns={columns.filter((e) =>
                permissions?.data
                  ?.map((permission) => {
                    return permission.code;
                  })
                  .includes(e.code)
              )}
              loading={loading}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
