import React, { useEffect, useState } from "react";
import { Button, Popover, Table, Tag, Typography } from "antd";
import { CloseCircleOutlined } from "@ant-design/icons";
import FilterTicketPerProfissional from "../../Modals/Filters/FilterTicketPerProfissional";
import "antd/dist/antd.css";
import "../../../styles/table.css";
import { otrsGet } from "../../../service/apiOtrs";
import { getTagColorBackground } from "../../../hooks/getTagColorBackground";

function TableTicketsPerProfissional(props) {
  const { Text } = Typography;
  const [popoverVisibility, setPopoverVisibility] = useState(false);
  const { tableData, users } = props;
  console.log(tableData);
  let data = tableData.ticketsData;

  const columns = [
    {
      title: "Cliente",
      dataIndex: "customer_name",
      key: "customer_name",
      render: (client_name) => <Text>{client_name ? client_name : "-"}</Text>,
    },
    {
      title: "Nº Ticket",
      dataIndex: "ticket_number",
      align: "center",
      key: "ticket_number",
      render: (ticket_number) => <Text>{ticket_number}</Text>,
    },
    {
      title: "Estado",
      dataIndex: "ticket_state",
      align: "center",
      key: "ticket_state",
      render: (ticket_state) => (
        <Tag color={getTagColorBackground(ticket_state)}>{ticket_state}</Tag>
      ),
    },
    {
      title: "Tipo",
      dataIndex: "ticket_type",
      align: "center",
      key: "ticket_type",
      render: (type) => <Text>{type || "Default"}</Text>,
    },
    {
      title: "Título",
      dataIndex: "ticket_title",
      key: "ticket_title",
      render: (subject) => <Text>{subject}</Text>,
    },
    {
      title: "Proprietario",
      dataIndex: "owner_ticket",
      align: "center",
      key: "owner_ticket",
      render: (_, user_name) => <Text>{tableData.name}</Text>,
    },
  ];

  function handlePopoverVisibility() {
    setPopoverVisibility(!popoverVisibility);
  }

  return (
    <div>
      <div
        className="table-card-top"
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "end",
          margin: "0px",
        }}
      >
        <Button
          type="text"
          onClick={() =>
            props.showTableTicketModal ? props.showModal() : props.showModal()
          }
        >
          <CloseCircleOutlined style={{ marginBottom: "5px" }} />
        </Button>
      </div>
      <div
        className="container-table"
        style={{ maxHeight: "60vh", overflow: "auto" }}
      >
        <Table columns={columns} dataSource={data} />
      </div>
    </div>
  );
}

export default TableTicketsPerProfissional;
