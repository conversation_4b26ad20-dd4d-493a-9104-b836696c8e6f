import { Col, Popover, Row, Table } from "antd";
import React, { useEffect, useState } from "react";
import "antd/dist/antd.css";
import "../../../styles/table.css";
import { FilterFilled } from "@ant-design/icons";
import ReservedHoursFilter from "../../Modals/Filters/ReservedHoursFilter";
import { SearchInput } from "../../SearchInput";

const columns = [
  {
    title: "Cliente",
    dataIndex: "clientName",
    key: "clientName",
    render: (clientName) => <p className="table-font">{clientName}</p>,
  },
  {
    title: "Nome do contrato",
    dataIndex: "contractName",
    key: "contractName",
    render: (contractName) => <p className="table-font">{contractName}</p>,
  },
  {
    title: "Tipo de contrato",
    dataIndex: "type",
    key: "type",
    render: (type) => <p className="table-font">{type}</p>,
  },
  {
    title: "Car<PERSON><PERSON>",
    dataIndex: "wallet",
    key: "wallet",
    render: (wallet) => <p className="table-font">{wallet}</p>,
  },
  {
    title: "Total mensal",
    dataIndex: "cosumedHours",
    key: "cosumedHours",
    render: (cosumedHours) => <p className="table-font">{cosumedHours}</p>,
  },
];

function ReservedHoursTable({
  allTicketsData,
  filteredData,
  arrayClients,
  arrayWallet,
  filteredContracts,
  getFilteredContracts,
  getFilteredContractByClient,
  getGraphData,
  showModal,
  modalVisible,
}) {
  const [popoverVisibility, setPopoverVisibility] = useState(false);
  const [dataTable, setDataTable] = useState([]);

  function handlePopoverVisibility() {
    setPopoverVisibility(!popoverVisibility);
  }

  function handleDataTable() {
    let tableData = [];
    filteredData.map((contract) => {
      tableData.push({
        wallet: contract.document_id,
        contractName: contract.name,
        clientName: ``,
        cosumedHours: 0,
        type: "-",
        contractId: contract.id,
        clientId: contract.customer_id,
      });
    });

    arrayClients.map((client) => {
      for (let contract of tableData) {
        if (client[1].customer_id === contract.clientId) {
          contract.clientName = client[1].name;
        }
      }
    });

    allTicketsData.map((month) => {
      month.map((contract) => {
        for (let client of tableData) {
          if (contract.contract_id === client.contractId) {
            client.cosumedHours += contract.uv;
          }
        }
      });
    });

    setDataTable(tableData);
  }

  useEffect(() => {
    handleDataTable();
  }, [showModal]);

  return (
    <Row gutter={[16, 16]}>
      <Col span={12}>
        <SearchInput
          placeholder="Pesquisar"
          onChange={(e) => {
            dataTable.filter((item) => {
              item.clientName
                .toLowerCase()
                .includes(e.target.value.toLowerCase());
            });
          }}
        />
      </Col>
      <Col span={24}>
        <Table columns={columns} dataSource={dataTable} />
      </Col>
    </Row>
  );
}

export default ReservedHoursTable;
