/**
 * Teste rápido para verificar URLs configuradas
 */

import { config } from './validateEnvironment';
import { getApiUrl, getCognitoUrl } from './devConfig';

export const testUrlConfiguration = () => {
  console.log('🔍 TESTE DE CONFIGURAÇÃO DE URLs');
  console.log('=' .repeat(50));
  
  console.log('📋 Variáveis de ambiente:');
  console.log('  REACT_APP_API_PERMISSION:', process.env.REACT_APP_API_PERMISSION);
  console.log('  REACT_APP_COGNITO_PARSE:', process.env.REACT_APP_COGNITO_PARSE);
  console.log('  REACT_APP_STAGE:', process.env.REACT_APP_STAGE);
  console.log('  NODE_ENV:', process.env.NODE_ENV);
  
  console.log('\n🔧 Configurações calculadas:');
  console.log('  config.apiUrl:', config.apiUrl);
  console.log('  config.cognitoUrl:', config.cognitoUrl);
  console.log('  getApiUrl():', getApiUrl());
  console.log('  getCognitoUrl():', getCognitoUrl());
  
  console.log('\n✅ URLs que DEVEM ser usadas:');
  console.log('  API:', 'https://api.dsm.darede.com.br/dev');
  console.log('  Cognito:', 'https://lukr7ocjz4.execute-api.us-east-1.amazonaws.com/dev/local');
  
  console.log('\n❌ URLs que NÃO devem aparecer:');
  console.log('  localhost:8000 (NUNCA)');
  console.log('  /api (NUNCA)');
  console.log('  /cognito-api (NUNCA)');
  
  // Verificar se há problemas
  const problems = [];
  
  if (config.apiUrl.includes('localhost')) {
    problems.push('config.apiUrl contém localhost');
  }
  
  if (getApiUrl().includes('localhost')) {
    problems.push('getApiUrl() contém localhost');
  }
  
  if (config.apiUrl.includes('/api')) {
    problems.push('config.apiUrl contém /api');
  }
  
  if (getApiUrl().includes('/api')) {
    problems.push('getApiUrl() contém /api');
  }
  
  if (problems.length > 0) {
    console.log('\n🚨 PROBLEMAS ENCONTRADOS:');
    problems.forEach(problem => console.log('  ❌', problem));
  } else {
    console.log('\n🎉 CONFIGURAÇÃO CORRETA!');
  }
  
  console.log('=' .repeat(50));
  
  return {
    configApiUrl: config.apiUrl,
    devConfigApiUrl: getApiUrl(),
    configCognitoUrl: config.cognitoUrl,
    devConfigCognitoUrl: getCognitoUrl(),
    problems
  };
};

// Executar teste automaticamente em desenvolvimento
if (process.env.NODE_ENV === 'development') {
  // Aguardar um pouco para garantir que tudo foi carregado
  setTimeout(() => {
    testUrlConfiguration();
  }, 1000);
}

export default testUrlConfiguration;
