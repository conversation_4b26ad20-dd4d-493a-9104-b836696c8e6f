import React, { useEffect, useMemo, useState } from "react";
import "antd/dist/antd.css";
import "../../../styles/table.css";
import { Button, Col, Row, Table, Typography } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { otrsGet } from "../../../service/apiOtrs";
import moment from "moment";
import { SearchInput } from "../../SearchInput";
import { filterTableData } from "../../../utils/filterTableData";
import { Counter } from "../../Counter";

function UnopenedTickets(props) {
  const { Text } = Typography;
  const [page, setPage] = useState(1);
  const { tableData, closeModal } = props;
  const [data, setData] = useState();
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);

  const getContractsByCustomer = async (customer_id) => {
    const contractRequest = await otrsGet(
      `read/contract/customer/${customer_id}`
    );
    return contractRequest.data;
  };

  const formatAccumulatedData = (contractRequest, customer, pageNumber) => {
    contractRequest = contractRequest.map((contract) => {
      return {
        customer_name: customer.customer_name,
        name: contract.name,
        document_id: contract.document_id,
        end_date: moment(contract.end_date).format("DD/MM/YYYY"),
        page: pageNumber,
      };
    });
    return contractRequest;
  };

  async function paginateCustomers(customers, pageSize, pageNumber) {
    setLoading(true);

    const firstPageContracts = await Promise.all(
      customers.slice(0, pageSize).map(async (customer) => {
        let contractRequest = await getContractsByCustomer(
          customer.customer_id
        );

        return formatAccumulatedData(
          Object.values(contractRequest),
          customer,
          pageNumber
        );
      })
    );

    setData(firstPageContracts.flat());
    setPage(pageNumber);
    setLoading(false);

    for (let i = pageSize; i < customers.length; i += pageSize) {
      (async () => {
        const pageContracts = await Promise.all(
          customers.slice(i, i + pageSize).map(async (customer) => {
            const contractRequest = await getContractsByCustomer(
              customer.customer_id
            );
            return formatAccumulatedData(
              Object.values(contractRequest),
              customer,
              pageNumber
            );
          })
        );

        setData((data) => [...data, ...pageContracts.flat()]);
        setPage((page) => page + 1);
      })();
    }
  }

  const handleCloseModal = () => {
    closeModal();
  };

  useEffect(() => {
    paginateCustomers(tableData, 10, page);
  }, [props.tableData]);

  const columns = [
    {
      title: "Cliente",
      dataIndex: "customer_name",
      key: "customer_name",
      align: "center",
      render: (customer_name) => <Text>{customer_name}</Text>,
    },
    {
      title: "Contrato",
      dataIndex: "name",
      key: "name",
      width: "40%",
      render: (name) => <Text>{name}</Text>,
    },
    {
      title: "Carteira",
      dataIndex: "document_id",
      key: "document_id",
      align: "center",
      render: (document_id) => <Text>{document_id ? document_id : "-"}</Text>,
    },
    {
      title: "Data de vencimento",
      dataIndex: "end_date",
      key: "end_date",
      align: "center",
      render: (end_date) => <Text>{end_date}</Text>,
    },
  ];

  const pagination = {
    data: [],
  };

  const filteredData = useMemo(() => {
    let filtered = data || [];

    const searchFields = ["customer_name", "name", "document_id", "end_date"];

    filtered = filterTableData({ data: filtered, search, searchFields });

    return filtered;
  }, [data, search]);

  return (
    <Row gutter={[16, 16]} justify="space-between" align="items-center">
      <Col span={12}>
        <SearchInput
          placeholder="Pesquisar"
          onChange={(value) => setSearch(value)}
        />
      </Col>
      <Col>
        <Button
          type="text"
          icon={<CloseOutlined />}
          onClick={handleCloseModal}
        ></Button>
      </Col>
      <div className="container-table">
        <Counter tableData={filteredData} />
        <Table
          loading={loading}
          columns={columns}
          dataSource={filteredData}
          pagination={pagination}
        />
      </div>
    </Row>
  );
}

export default UnopenedTickets;
