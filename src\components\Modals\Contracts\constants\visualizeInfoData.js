export const tabsData = [
  {
    title: "Informações temporais",
    permissionCode: "view_info_modal_tab_temporal_info",
    key: "date_info",
    fieldKeys: [
      "expected_start_date",
      "expected_close_date",
      "created_at",
      "updated_at",
    ],
  },
  {
    title: "Informações gerais",
    permissionCode: "view_info_modal_tab_general_info",
    key: "general_info",
    fieldKeys: [
      "name",
      "dsm_id",
      "itsm_id",
      "crm_id",
      "total_hours",
      "squad",
      "type_hours",
      "scope",
      "approved_proposal",
      "block",
      "freezing",
      "block_emails",
      "status",
      "pool_type",
    ],
  },
  {
    title: "Dados financeiros",
    permissionCode: "view_info_modal_tab_financial_info",
    key: "financial_info",
    fieldKeys: ["value", "aws_value", "excess_cost"],
  },
  {
    title: "Cliente",
    permissionCode: "view_info_modal_tab_client_info",
    key: "client_info",
    fieldKeys: ["customer_itsm", "customer_name", "customer_cnpj"],
  },
  {
    title: "Time de contratos",
    permissionCode: "view_info_modal_tab_contract_team_info",
    key: "contract_team_info",
    fieldKeys: [
      "billed_value",
      "excess_hour_value",
      "reajustment_index",
      "reajustment_percentage",
      "automatic_renewal",
      "signed",
      "signature_date",
      "rescission_fine",
      "sla_fine",
      "cancellation_date",
      "notice_rescission",
    ],
  },
  {
    title: "Time de governaça/financeiro",
    permissionCode: "view_info_modal_tab_governance_team_info",
    key: "governance_team_info",
    fieldKeys: [
      "invoice",
      "purchase_order",
      "invoice_amount",
      "invoice_status",
      "reajustment_period",
      "target",
      "performed_period",
      "service_start_date",
      "contract_deadline",
      "contract_bond",
      "service_start_date",
      "expected_value",
    ],
  },
];
