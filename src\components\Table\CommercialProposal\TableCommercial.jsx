import { Row, Col, Input, Table, Select, Tag, Button } from 'antd'
import { useState } from 'react'
import {
  PaperClipOutlined,
  CheckOutlined,
  LikeOutlined
} from '@ant-design/icons'
import { ViewServiceModal } from '../../Modals/TechnicalProposals/ViewService'

const { Option } = Select

export const TableCommercial = () => {
  const [loading, setLoading] = useState(false)
  const [search, setSearch] = useState(false)

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      sorter: (a, b) => a.id - b.id,
      sortDirections: ['descend', 'ascend']
    },
    {
      title: 'Nome',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Descrição',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Tag',
      dataIndex: 'tag',
      key: 'tag',
      render: item => {
        return <Tag color={item.color}>{item.name}</Tag>
      }
    },
    {
      title: '8x5 SETUP',
      dataIndex: 'firstSetup',
      key: 'firstSetup'
    },
    {
      title: '24x7 SETUP',
      dataIndex: 'secondSetup',
      key: 'secondSetup'
    }
  ]
  const data = [
    {
      id: '32',
      name: 'teste',
      description: 'teste de descrição',
      tag: {
        name: 'tag disso',
        color: 'blue'
      },
      firstSetup: '0H',
      secondSetup: '0H',
      viewer: ''
    }
  ]

  return (
    <>
      <div style={{ padding: '2em' }}>
        <Row justify="end">
          <Col
            lg={6}
            sm={24}
            style={{
              display: 'flex',
              flexDirection: 'row',
              marginRight: '30px'
            }}
          >
            <p style={{ marginRight: '10px', width: '40%' }}>Filtrar por:</p>
            <Select style={{ width: '100%' }} defaultValue="lucy">
              <Option value="jack">Jack</Option>
              <Option value="lucy">Lucy</Option>
              <Option value="Yiminghe">yiminghe</Option>
            </Select>
          </Col>
          <Col lg={6} sm={24}>
            <Input
              onChange={e => setSearch(e.target.value)}
              style={{
                width: '100%',
                height: '35px',
                borderRadius: '7px'
              }}
              placeholder="Buscar..."
            />
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Table
              loading={loading}
              dataSource={data}
              scroll={{ x: '100%' }}
              columns={columns}
              style={{ minWidth: '100%', marginTop: '20px' }}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={15}>
            <p>
              Editado pela última vez por Fulano da Silva às 14:14h em
              10/06/2022
            </p>
          </Col>
          <Col>
            <Row gutter={16}>
              <Col sapn={6}>
                <Button>Pré-visualisar</Button>
              </Col>
              <Col sapn={6}>
                <Button icon={<PaperClipOutlined />}>Anexos</Button>
              </Col>
              <Col sapn={6}>
                <Button type="primary" icon={<CheckOutlined />}>
                  Salvar
                </Button>
              </Col>
              <Col sapn={6}>
                <Button icon={<LikeOutlined />}>Gerar Proposta</Button>
              </Col>
            </Row>
          </Col>
        </Row>
      </div>
    </>
  )
}
