import React, { useEffect, useState } from 'react'
import { Row, Col, Input, Typography, Select, Form, Button, Space } from 'antd'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { AddArchitect } from '../../components/Modals/TechnicalProposals/AddArchitect'
import { AddBU } from '../../components/Modals/TechnicalProposals/AddBU'
import { AddContact } from '../../components/Modals/TechnicalProposals/AddContact'
import { AddOpportunity } from '../../components/Modals/TechnicalProposals/AddOportunity'
import { useCognitoGet } from '../../service/apiCognito'
import { useLocation, useParams, useSearchParams } from 'react-router-dom'
import { v4 } from 'uuid'
import axios from 'axios'
import { dynamoGet } from '../../service/apiDsmDynamo'
import { authService } from '../../services/authService'

export const CommercialProposalHeader = proposalProps => {
  const { state } = useLocation()
  // const [proposalType, setProposaltype] = useSearchParams()
  // console.log('proposalType: ', proposalType)
  const { Option } = Select
  const { Title } = Typography
  const [allCustomers, setAllCustomers] = useState([])
  const [selectedCustomer, setSelectedCustomer] = useState(
    state !== null || localStorage.getItem('technical-proposal/customer')
      ? {
          name: state?.customer?.name
            ? state.customer.name
            : JSON.parse(localStorage.getItem('technical-proposal/customer'))
                .client_name,
          contacts: state?.customer?.contacts
            ? state.customer.contacts
            : JSON.parse(localStorage.getItem('technical-proposal/customer'))
                .contacts,
          cnpj: state?.customer?.cnpj
            ? state.customer.cnpj
            : JSON.parse(localStorage.getItem('technical-proposal/customer'))
                .cnpj
        }
      : {
          name: '',
          contacts: [],
          cnpj: ''
        }
  )

  const [contacts, setContacts] = useState(
    selectedCustomer ? selectedCustomer.contacts : []
  )
  const [types, setTypes] = useState([])
  const [selectedType, setSelectedType] = useState('')
  const [commercialStatus, setCommercialStatus] = useState([
    'não incializada',
    'em andamento',
    'concluída'
  ])
  const [selectedStatus, setSelectedStatus] = useState('')
  const [oportunities, setOportunities] = useState([])
  const [architects, setArchitects] = useState([])
  const [selectedArchitect, setSelectedArchitect] = useState({})
  const [bus, setBus] = useState([])
  const [selectedBU, setSelectedBU] = useState('')
  const [loading, setLoading] = useState(false)

  const headerInputs = [
    {
      title: 'Nome do Projeto',
      placeholder: 'Título da proposta',
      inputType: 'Input',
      formName: 'project_name',
      value: state?.name
    },
    {
      title: 'Cliente',
      placeholder: 'Nome fantasia do cliente',
      inputType: 'Select',
      formName: 'client_name',
      value: state?.customer?.name
    },
    {
      title: 'Tipo',
      placeholder: 'Tipo da proposta',
      inputType: 'Select',
      formName: 'project_type',
      value: state?.type
    },
    {
      title: 'Status Comercial',
      placeholder: 'Status da proposta',
      inputType: 'Select',
      formName: 'project_status',
      value: state?.commercialStatus
    }
  ]

  const headerInputsWithList = [
    {
      title: 'Contatos',
      placeholder: 'Nome do contato',
      inputType: 'Select',
      formName: 'project_contacts',
      component: <AddContact />,
      value:
        state !== null
          ? state?.customer?.contacts[0]?.names?.first_name !== undefined
            ? state?.customer?.contacts[0]?.names?.first_name
            : JSON.parse(localStorage.getItem('technical-proposal/customer'))
                .contacts
          : []
    },
    {
      title: 'Arquitetos',
      placeholder: 'Nome do arquiteto',
      inputType: 'Select',
      formName: 'project_architects',
      component: <AddArchitect />,
      value: state?.architects
    },
    {
      title: 'BUs',
      placeholder: 'Nome da unidade de negócio',
      inputType: 'Select',
      formName: 'project_bu',
      component: <AddBU state={state?.bus} />,
      value: state?.bus
    },
    {
      title: 'Oportunidade',
      placeholder: 'Nome da oportunidade',
      inputType: 'Select',
      formName: 'project_opportunity',
      component: <AddOpportunity />,
      value: state?.opportunity
    }
  ]

  const handleSelectCustomer = value => {
    const customer = allCustomers?.find(c => c?.names?.name === value)
    setSelectedCustomer(customer)
  }

  useEffect(() => {
    setSelectedCustomer({
      ...selectedCustomer,
      contacts: contacts
    })
  }, [contacts])

  useEffect(() => {
    if (proposalProps) {
      proposalProps?.setCurrentContacts(selectedCustomer)
      setContacts(selectedCustomer.contacts)
    }
  }, [selectedCustomer])

  useEffect(() => {
    setLoading(true)

    async function getCustomers() {
      const tableCustomers = await dynamoGet(
        `${process.env.REACT_APP_STAGE}-customers`
      )

      const filteredByExistingName = tableCustomers.filter(c => c?.names?.name)

      const filterDuplicatedNames = filteredByExistingName.filter(
        (c, i, self) =>
          i === self.findIndex(t => t?.names?.name === c?.names?.name)
      )

      setAllCustomers(filterDuplicatedNames)
    }

    async function getUsers() {
      try {
        const { data } = await axios.get(
          `${process.env.REACT_APP_API_PERMISSION}cognito/read`,
          {
            headers: authService.getAuthHeaders()
          }
        )
        let users = []
        for (let i = 0; i < data.data.length; i++) {
          users.push(data.data[i])
        }
        setArchitects(users)
      } catch (error) {
        console.error('Erro ao buscar usuários:', error);
        setArchitects([]);
      }
    }

    async function getBus() {
      const response = await dynamoGet(`${process.env.REACT_APP_STAGE}-wallets`)

      setBus(response)
    }

    async function getTypes() {
      let response = await dynamoGet('hml-crm-custom-fields')

      const consumeTypes = response[0].deal.find(
        deal => deal.name === 'Tipo de Consumo'
      )

      let labels = consumeTypes.options.map(option => option.label)

      setTypes(labels)
    }

    async function getAllData() {
      await getCustomers()
      await getTypes()
      await getBus()
      await getUsers()
    }

    getAllData()
    setLoading(false)
  }, [])

  return (
    <Col span={24}>
      <Row
        justify="space-between"
        style={{ marginBottom: '15px' }}
        gutter={[4, 0]}
      >
        {headerInputs.map((headerInput, index) => {
          if (headerInput.title === 'Cliente') {
            return (
              <Col sm={24} md={12} lg={6} key={index}>
                <Row>
                  <Title level={5} style={{ fontWeight: '400' }}>
                    {headerInput.title}
                  </Title>
                </Row>
                <Row>
                  <Col span={23}>
                    <Form.Item
                      name={headerInput.formName}
                      initialValue={headerInput.value}
                      rules={[
                        {
                          required: true,
                          message: 'Por favor, preencha o campo'
                        }
                      ]}
                    >
                      <Select
                        loading={allCustomers?.length <= 0 ? true : false}
                        showSearch
                        placeholder={headerInput.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.children
                            ?.toLowerCase()
                            .startsWith(input?.toLowerCase()) ||
                          option?.children
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA?.children
                            ?.toLowerCase()
                            .localeCompare(optionB?.children?.toLowerCase())
                        }
                        onSelect={handleSelectCustomer}
                      >
                        {allCustomers?.map(c => (
                          <Option
                            value={c?.names?.name ? c?.names?.name : 'N/A'}
                          >
                            {c?.names?.name}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
            )
          }

          if (headerInput.title === 'Tipo') {
            return (
              <Col sm={24} md={12} lg={6} key={index}>
                <Row>
                  <Title level={5} style={{ fontWeight: '400' }}>
                    {headerInput.title}
                  </Title>
                </Row>
                <Row>
                  <Col span={23}>
                    <Form.Item
                      name={headerInput.formName}
                      initialValue={headerInput.value}
                      rules={[
                        {
                          required: true,
                          message: 'Por favor, preencha o campo'
                        }
                      ]}
                    >
                      <Select
                        loading={types?.length <= 0 ? true : false}
                        showSearch
                        placeholder={headerInput.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.children
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA.children
                            .toLowerCase()
                            .localeCompare(optionB.children.toLowerCase())
                        }
                        onSelect={e => setSelectedType(e.target)}
                      >
                        {types.map(t => (
                          <Option value={t}>{t}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
            )
          }

          if (headerInput.title === 'Status Comercial') {
            return (
              <Col sm={24} md={12} lg={6} key={index}>
                <Row>
                  <Title level={5} style={{ fontWeight: '400' }}>
                    {headerInput.title}
                  </Title>
                </Row>
                <Row>
                  <Col span={23}>
                    <Form.Item
                      name={headerInput.formName}
                      initialValue={headerInput.value}
                      rules={[
                        {
                          required: true,
                          message: 'Por favor, preencha o campo'
                        }
                      ]}
                    >
                      <Select
                        loading={commercialStatus?.length <= 0 ? true : false}
                        showSearch
                        placeholder={headerInput.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.children
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA.children
                            .toLowerCase()
                            .localeCompare(optionB.children.toLowerCase())
                        }
                        onSelect={e => setSelectedStatus(e.target)}
                      >
                        {commercialStatus.map(t => (
                          <Option value={t}>{t}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
            )
          }

          return (
            <Col sm={24} md={12} lg={6} key={index}>
              <Row>
                <Title level={5} style={{ fontWeight: '400' }}>
                  {headerInput.title}
                </Title>
              </Row>
              <Row>
                <Col span={23}>
                  {headerInput.inputType === 'Input' ? (
                    <Form.Item
                      name={headerInput.formName}
                      initialValue={headerInput.value}
                      rules={[
                        {
                          required: true,
                          message: 'Por favor, preencha o campo'
                        }
                      ]}
                    >
                      <Input
                        placeholder={headerInput.placeholder}
                        style={{ width: '100%' }}
                      ></Input>
                    </Form.Item>
                  ) : (
                    <Form.Item
                      name={headerInput.formName}
                      rules={[
                        {
                          required: true,
                          message: 'Por favor, preencha o campo'
                        }
                      ]}
                    >
                      <Select
                        showSearch
                        placeholder={headerInput.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option.children.includes(input)
                        }
                        filterSort={(optionA, optionB) =>
                          optionA.children
                            .toLowerCase()
                            .localeCompare(optionB.children.toLowerCase())
                        }
                      ></Select>
                    </Form.Item>
                  )}
                </Col>
              </Row>
            </Col>
          )
        })}
      </Row>

      <Row
        justify="space-between"
        align="middle"
        style={{ marginBottom: '2rem' }}
        gutter={[12, 0]}
      >
        {headerInputsWithList.map((headerInputWithList, index) => {
          if (headerInputWithList.title === 'Contatos') {
            return (
              <Col sm={24} md={12} lg={6}>
                <Row>
                  <Title level={5} style={{ fontWeight: '400' }}>
                    {headerInputWithList.title}
                  </Title>
                </Row>

                <Row>
                  <Col span={20}>
                    <Form.Item
                      initialValue={headerInputWithList.value}
                      name={headerInputWithList.formName}
                      rules={[
                        {
                          required: true,
                          message: 'Por favor, preencha o campo'
                        }
                      ]}
                    >
                      <Select
                        showSearch
                        placeholder={headerInputWithList.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.children
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA.children
                            .toLowerCase()
                            .localeCompare(optionB.children.toLowerCase())
                        }
                      >
                        {selectedCustomer
                          ? selectedCustomer.contacts?.map(c => {
                              return (
                                <Option value={c.names?.first_name}>
                                  {c.names?.first_name}
                                </Option>
                              )
                            })
                          : null}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    {selectedCustomer ? (
                      <AddContact
                        contactsParent={contacts}
                        setContacts={contacts => setContacts(contacts)}
                      />
                    ) : (
                      <AddContact
                        contactsParent={null}
                        setContacts={contacts => setContacts(contacts)}
                      />
                    )}
                  </Col>
                </Row>
              </Col>
            )
          }

          if (headerInputWithList.title === 'Arquitetos') {
            return (
              <Col sm={24} md={12} lg={6} key={index}>
                <Row>
                  <Title level={5} style={{ fontWeight: '400' }}>
                    {headerInputWithList.title}
                  </Title>
                </Row>
                <Row>
                  <Col span={23}>
                    <Form.Item
                      name={headerInputWithList.formName}
                      initialValue={headerInputWithList.value}
                      rules={[
                        {
                          required: true,
                          message: 'Por favor, preencha o campo'
                        }
                      ]}
                    >
                      <Select
                        loading={architects?.length <= 0 ? true : false}
                        mode="multiple"
                        showSearch
                        placeholder={headerInputWithList.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.children
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA.children
                            ?.toLowerCase()
                            .localeCompare(optionB.children?.toLowerCase())
                        }
                      >
                        {architects.map(t => (
                          <Option value={t.email}>{t.email}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  {/* <Col span={4}>
                    {selectedArchitect ? (
                      <AddArchitect
                        architectsParent={architects}
                        setArchitects={architects => setArchitects(architects)}
                      />
                    ) : (
                      <AddArchitect
                        architectsParent={architects}
                        setArchitects={architects => setArchitects(architects)}
                      />
                    )}
                  </Col> */}
                </Row>
              </Col>
            )
          }

          if (headerInputWithList.title === 'BUs') {
            return (
              <Col sm={24} md={12} lg={6} key={index}>
                <Row>
                  <Title level={5} style={{ fontWeight: '400' }}>
                    {headerInputWithList.title}
                  </Title>
                </Row>
                <Row>
                  <Col span={20}>
                    <Form.Item
                      name={headerInputWithList.formName}
                      initialValue={headerInputWithList.value}
                      rules={[
                        {
                          required: true,
                          message: 'Por favor, preencha o campo'
                        }
                      ]}
                    >
                      <Select
                        loading={bus?.length <= 0 ? true : false}
                        mode="multiple"
                        showSearch
                        placeholder={headerInputWithList.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.children
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA.children
                            .toLowerCase()
                            .localeCompare(optionB.children.toLowerCase())
                        }
                      >
                        {bus.map(t => (
                          <Option value={t.name}>{t.name}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    {selectedBU ? (
                      <AddBU busParent={bus} setBus={bus => setBus(bus)} />
                    ) : (
                      <AddBU busParent={bus} setBus={bus => setBus(bus)} />
                    )}
                  </Col>
                </Row>
              </Col>
            )
          }

          if (headerInputWithList.title === 'Oportunidade') {
            return (
              <Col sm={24} md={12} lg={6} key={index}>
                <Row>
                  <Title level={5} style={{ fontWeight: '400' }}>
                    {headerInputWithList.title}
                  </Title>
                </Row>
                <Row>
                  <Col span={20}>
                    <Form.Item
                      name={headerInputWithList.formName}
                      initialValue={headerInputWithList.value}
                      rules={[
                        {
                          required: true,
                          message: 'Por favor, preencha o campo'
                        }
                      ]}
                    >
                      <Select
                        // loading={oportunities?.length <= 0 ? true : false}
                        showSearch
                        placeholder={headerInputWithList.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.children
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        filterSort={(optionA, optionB) =>
                          optionA.children
                            .toLowerCase()
                            .localeCompare(optionB.children.toLowerCase())
                        }
                        // onSelect={e => setSelectedOportunity(e.target)}
                      >
                        {oportunities.map(t => (
                          <Option value={t}>{t}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    {selectedCustomer ? (
                      <AddOpportunity
                        oportunitiesParent={oportunities}
                        setOportunities={oportunities =>
                          setOportunities(oportunities)
                        }
                      />
                    ) : (
                      <AddOpportunity
                        oportunitiesParent={oportunities}
                        setOportunities={oportunities =>
                          setOportunities(oportunities)
                        }
                      />
                    )}
                  </Col>
                </Row>
              </Col>
            )
          }

          return (
            <Col sm={24} md={12} lg={6}>
              <Row>
                <Title level={5} style={{ fontWeight: '400' }}>
                  {headerInputWithList.title}
                </Title>
              </Row>
              <Row>
                <Col span={20}>
                  <Form.Item
                    name={headerInputWithList.formName}
                    rules={[
                      {
                        required: true,
                        message: 'Por favor, preencha o campo'
                      }
                    ]}
                  >
                    <Select
                      placeholder={headerInputWithList.placeholder}
                      style={{ width: '100%' }}
                    ></Select>
                  </Form.Item>
                </Col>
                <Col span={4}>{headerInputWithList.component}</Col>
              </Row>
            </Col>
          )
        })}
      </Row>
    </Col>
  )
}
