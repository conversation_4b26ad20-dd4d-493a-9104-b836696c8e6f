import React, {useState, useEffect} from 'react'
import {Table, Select} from 'antd'
import {ArrowLeftOutlined} from '@ant-design/icons'
import 'antd/dist/antd.css'
import {TotalHoursByCategory} from '../TotalHoursByCategory'
import './clientByCategoryStyle.css'

const ClientByCategory = ({allClients, allContracts, categoryName, sumClientsPerCategory, showModal}) => {
    const [tablePage, setTablePage] = useState(true)
    const [data, setData] = useState([])
    const [clientName, setClientName] = useState("")
    const [filteredContracts, setFilteredContracts] = useState([])
    const [contractSelected, setContractSelected] = useState([])
    const { Option } = Select;

    const columns = [
        {
            title: 'Client',
            dataIndex: 'client',
            key: 'client',
            render: (client) => <a onClick={() => {
                showContracts(client)
                getFilteredClient(client)
            }}  className="table-font">{client}</a>,
        },
        {
            title: 'Categoria',
            dataIndex: 'category',
            key: 'category',
            popupClassName: 'test',
            render: (category) => {
                return (
                    <>
                        <Select
                            mode="default"
                            placeholder={categoryName}
                            onChange={(value) => {
                                changeCategory(value)
                                getClientsCategory(categoryName)
                                sumClientsPerCategory()
                            }}
                        >
                            <Option className="select-vip" key={'VIP'} value={'VIP'}>{'VIP'}</Option>
                            <Option className="select-prime" key={'Prime'} value={'Prime'}>{'Prime'}</Option>
                            <Option className="select-gold" key={'Gold'} value={'Gold'}>{'Gold'}</Option>
                            <Option className="select-silver" key={'Silver'} value={'Silver'}>{'Silver'}</Option>
                        </Select>
                    </>
            )
            },
        },
        {
            title: 'Qntd. Contratos',
            dataIndex: 'amountOfContracts',
            key: 'amountOfContracts',
            render: (amountOfContracts) => <p className="table-font">{amountOfContracts}</p>,
        },
    ]

    let filteredClient = []

    function showContracts(client) {
        setClientName(client)
        setTablePage(!tablePage)
    }

    useEffect(() => {
        getClientsCategory(categoryName)
    }, [filteredContracts])

    function getClientsCategory(categoryName) {
        allClients.map((client) => {
            if(client.category === categoryName) {
                filteredClient.push(client)
            }
        })

        setData(filteredClient)
    }

    function getFilteredClient(name) {
        allClients.map((client) => {
            if(name === client.client) {
                getFilteredContracts(client.id)
            }
        })
    }

    function getFilteredContracts(id) {
        let selectedContracts = []
        Object.entries(allContracts).forEach((contract) => {
            if(contract[1].customer_id == id) {
                selectedContracts.push(contract[1])
            }
        })

        setFilteredContracts(selectedContracts)
    }


    function getContractSelected(contract) {
        setContractSelected((contract))
    }

    function changeCategory(value) {
        contractSelected.category = value;
    }

    return (
        <div style={{padding: '20px'}} className="card">
            {tablePage === true ? (
                <>
                    <div className="table-card-top table-totalHoursCategory">
                        <div className="button">
                            <button className="filter-clear-btn button-back" onClick={() => showModal()}>
                                <ArrowLeftOutlined/>
                            </button>
                        </div>
                        <h2>{categoryName}</h2>
                    </div>
                    <div>
                        <Table
                            onRow={(record, rowIndex, value) => {
                                return {
                                    onClick: event => { {
                                        getContractSelected(record)
                                    }}
                                }
                            }}
                            scroll={{y: 200}}
                            pagination={false}
                            columns={columns}
                            dataSource={data}
                        >
                        </Table>
                    </div>
                </>
            ) : (
                <TotalHoursByCategory contractSelected={contractSelected} filteredContracts={filteredContracts} showContracts={showContracts}/>
            )}
        </div>
    )
}

export {ClientByCategory}