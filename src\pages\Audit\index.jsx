import React, { useEffect, useMemo, useState } from "react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import { Layout, Card, Row, Col, Input, DatePicker, Typography } from "antd";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import { Table } from "antd";
import { format, getMonth, getYear } from "date-fns";
import useSWR from "swr";
import axios from "axios";
import moment from "moment";
import { Counter } from "../../components/Counter";

const { Content } = Layout;

export const Audit = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [date, setDate] = useState(
    localStorage.getItem("auditsDate")
      ? new Date(localStorage.getItem("auditsDate"))
      : new Date()
  );
  const { Text } = Typography;

  useEffect(() => {
    window.scrollTo({ left: 0, top: 0, behavior: "smooth" });
  }, []);

  const onChange = ({ _d }) => {
    localStorage.setItem("auditsDate", new Date(_d));
    setDate(new Date(_d));
  };

  const { data } = useSWR(`audits-${date}`, async () => {
    let { audits, nextPage } = await getAudits();
    while (nextPage) {
      let response = await getAudits(nextPage);
      nextPage = response.nextPage;
      audits.push(...response.audits);
    }

    return {
      Items: audits,
    };
  });

  async function getAudits(nextPage = "") {
    const jwt = localStorage.getItem("jwt");

    const month = getMonth(new Date(date)) + 1;
    const year = getYear(new Date(date));

    let params = [];
    if (nextPage) params.push(`?nextPage=${nextPage}`);

    const { data } = await axios.get(
      `${
        process.env.REACT_APP_API_PERMISSION
      }read/audits/${month}/${year}${params.join("&")}`,
      {
        headers: {
          Authorization: jwt,
        },
      }
    );
    return {
      audits: data.data.audits,
      nextPage: data.data.nextPage,
    };
  }

  const permissions = useSWR("audit", async () => {
    let data = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-permissions`,
      localStorage.getItem("@dsm/permission")
    );

    return [...data.permissions.find((x) => x.page === "Auditoria").actions];
  });

  const filterBySearch = (data, search) => {
    let filteredData = [];

    if (data) {
      filteredData = data.filter((e) => {
        let verifyName,
          verifyDescription,
          verifyData = false;

        if (e.name) {
          verifyName = e.name
            .toString()
            .toLowerCase()
            .includes(search.toLowerCase());
        }

        if (e.description) {
          verifyDescription = e.description
            .toString()
            .toLowerCase()
            .includes(search.toLowerCase());
        }

        if (e.created_at) {
          verifyData = format(
            new Date(e.created_at),
            "dd/MM/yyyy HH:mm"
          ).includes(search);
        }

        if (verifyName || verifyDescription || verifyData) return e;
      });
    }

    return filteredData;
  };

  const tableData = useMemo(() => {
    let filteredData = data?.Items || [];
    if (search !== "") {
      filteredData = filterBySearch(filteredData, search);
    }

    return filteredData;
  }, [data?.Items, search, data]);

  const columns = [
    {
      code: "view_title",
      title: "Nome",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_description",
      title: "Descrição",
      dataIndex: "description",
      key: "description",
      sorter: (a, b) => a.description.localeCompare(b.description),
      sortDirections: ["descend", "ascend"],
    },

    {
      code: "view_date",
      title: "Data",
      dataIndex: "created_at",
      key: "created_at",
      defaultSortOrder: "descend",
      sortDirections: ["descend", "ascend"],
      sorter: (a, b) => new Date(a?.created_at) - new Date(b?.created_at),
      render: (created_at) => {
        return <p>{format(new Date(created_at), "dd/MM/yyyy HH:mm")}</p>;
      },
    },
  ];

  if (!data && !loading) {
    return setLoading(true);
  }

  if (data && loading) {
    return setLoading(false);
  }

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Row justify="space-between">
              <DatePicker
                defaultValue={moment(format(date, "yyyy/MM"), "YYYY/MM")}
                onChange={onChange}
                picker="month"
              />
              <Input
                onChange={(e) => setSearch(e.target.value)}
                style={{
                  width: "300px",
                  height: "35px",
                  borderRadius: "7px",
                  marginBottom: "1rem",
                }}
                placeholder="Buscar alteração..."
              />
            </Row>
            <Counter tableData={tableData} />
            <Row>
              <Col span={24}>
                <Table
                  scroll={{ x: "100%" }}
                  dataSource={tableData}
                  columns={columns.filter((e) =>
                    permissions?.data
                      ?.map((permission) => {
                        return permission.code;
                      })
                      .includes(e.code)
                  )}
                  loading={loading}
                />
              </Col>
            </Row>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
