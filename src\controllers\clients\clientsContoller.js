import axios from "axios";

export const filterBySearch = (data, search) => {
  if (!data || !search) {
    return [];
  }

  const searchLower = search.toLowerCase();

  const filteredData = data.filter((item) => {
    if (item.accounts?.length > 0) {
      if (
        item.accounts.some((account) =>
          account.account_id.includes(searchLower)
        )
      ) {
        return true;
      }
    }
    if (
      item.identifications?.itsm_id &&
      item.identifications.itsm_id
        .toString()
        .toLowerCase()
        .includes(searchLower)
    ) {
      return true;
    }

    if (item.cnpj && item.cnpj.toString().toLowerCase().includes(searchLower)) {
      return true;
    }

    if (
      item.names?.name &&
      item.names.name.toLowerCase().includes(searchLower)
    ) {
      return true;
    }

    if (item.name && item.name.toLowerCase().includes(searchLower)) {
      return true;
    }

    if (
      item.names?.fantasy_name &&
      item.names.fantasy_name.toLowerCase().includes(searchLower)
    ) {
      return true;
    }

    return false;
  });

  return filteredData;
};

export const filterContracts = (contracts, customer, state) => {
  if (!contracts || !customer || !state) return [];
  switch (state) {
    case "all":
      return contracts?.filter(
        (contract) =>
          (contract?.customer?.id || contract.customer?.itsm_id) &&
          (contract?.customer?.id?.toString() === customer?.id?.toString() ||
            contract.customer?.itsm_id?.toString() ===
              customer.identifications?.itsm_id?.toString())
      );

    case "ativosA":
      return contracts?.filter(
        (contract, index, array) =>
          (contract?.customer?.id || contract.customer?.itsm_id) &&
          (contract?.customer?.id?.toString() === customer?.id?.toString() ||
            contract.customer?.itsm_id?.toString() ===
              customer.identifications?.itsm_id?.toString()) &&
          array
            ?.filter(
              (contract) =>
                (contract?.customer?.id || contract.customer?.itsm_id) &&
                (contract?.customer?.id?.toString() ===
                  customer?.id?.toString() ||
                  contract.customer?.itsm_id?.toString() ===
                    customer.identifications?.itsm_id?.toString())
            )
            .some((x) => x.active)
      );

    case "ativosI":
      return contracts?.filter(
        (contract, index, array) =>
          (contract?.customer?.id || contract.customer?.itsm_id) &&
          (contract?.customer?.id?.toString() === customer?.id?.toString() ||
            contract.customer?.itsm_id?.toString() ===
              customer.identifications?.itsm_id?.toString()) &&
          array
            ?.filter(
              (contract) =>
                (contract?.customer?.id || contract.customer?.itsm_id) &&
                (contract?.customer?.id?.toString() ===
                  customer?.id?.toString() ||
                  contract.customer?.itsm_id?.toString() ===
                    customer.identifications?.itsm_id?.toString())
            )
            .every((x) => !x.active)
      );

    case "prospect":
      return !contracts?.filter(
        (contract, index, array) =>
          (contract?.customer?.id || contract.customer?.itsm_id) &&
          (contract?.customer?.id?.toString() === customer?.id?.toString() ||
            contract.customer?.itsm_id?.toString() ===
              customer.identifications?.itsm_id?.toString())
      ).length;

    default:
      break;
  }
};

export const filterByState = (data, state, contracts) => {
  if (!data || !state) return [];
  switch (state) {
    case "ativosA":
      return data?.filter(
        (e) =>
          e.active === 1 &&
          filterContracts(contracts?.data, e, "ativosA")?.length
      );
    case "inativosC":
      return data?.filter((e) => e?.active === 0);
    case "ativosI":
      return data?.filter(
        (e) =>
          e?.active === 1 &&
          filterContracts(contracts?.data, e, "ativosI")?.length
      );
    case "prospect":
      return data?.filter(
        (e) =>
          e?.active === 1 && filterContracts(contracts?.data, e, "prospect")
      );
    case "todos":
    default:
      return data;
  }
};

export const getCustomerContracts = async (customerId) => {
  try {
    const { data } = await axios.get(
      process.env.REACT_APP_API_PERMISSION + "contracts",
      {
        params: {
          customer_id: customerId,
        },
        headers: {
          Authorization: localStorage.getItem("jwt"),
        },
      }
    );
    return data;
  } catch (error) {
    console.log(error);
  }
};

export const getCustomerByParameter = async (route, params) => {
  try {
    const customers = await axios.get(
      process.env.REACT_APP_API_PERMISSION + route,
      {
        params: params,
        headers: {
          dynamodb: `${process.env.REACT_APP_STAGE}-customers`,
          Authorization: localStorage.getItem("jwt"),
        },
      }
    );

    return customers.data.data.Items;
  } catch (error) {
    console.log(error);
  }
};

export async function getAllCustomers() {
  let { data, nextPage } = await getPaginateCustomers();
  while (nextPage) {
    let response = await getPaginateCustomers(nextPage);
    nextPage = response.nextPage;
    data.push(...response.data);
  }

  return {
    Items: data,
  };
}

async function getPaginateCustomers(nextPage = "") {
  const jwt = localStorage.getItem("jwt");

  let params = [];
  if (nextPage) params.push(`?nextPage=${nextPage}`);

  const { data } = await axios.get(
    `${process.env.REACT_APP_API_PERMISSION}read/paginate${params.join("&")}`,
    {
      headers: {
        Authorization: jwt,
        dynamodb: `${process.env.REACT_APP_STAGE}-customers`,
      },
    }
  );

  return {
    data: data.data,
    nextPage: data.nextPage,
  };
}

export async function getCustomers() {
  const activeCustomersPromise = new Promise(async (resolve, reject) => {
    const route = "customers/read/status";
    const customers = await getCustomerByParameter(route, { isActive: 1 });
    resolve(customers);
  });

  const inactiveCustomersPromise = new Promise(async (resolve, reject) => {
    const route = "customers/read/status";
    const customers = await getCustomerByParameter(route, { isActive: 0 });
    resolve(customers);
  });

  const customers = await Promise.all([
    activeCustomersPromise,
    inactiveCustomersPromise,
  ]);

  return { Items: customers.flat() };
}
