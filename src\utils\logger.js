/**
 * Sistema de logging configurável para a aplicação
 */

import { config } from './validateEnvironment';

// Níveis de log
export const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

// Configuração do logger baseada no ambiente
const loggerConfig = {
  level: config.isDevelopment ? LOG_LEVELS.DEBUG : LOG_LEVELS.WARN,
  enableConsole: true,
  enableRemote: config.isProduction,
  maxLogSize: 1000, // máximo de logs em memória
  sensitiveFields: ['password', 'token', 'jwt', 'authorization']
};

// Buffer de logs para envio remoto
let logBuffer = [];

/**
 * Remove dados sensíveis do objeto de log
 */
const sanitizeLogData = (data) => {
  if (!data || typeof data !== 'object') return data;
  
  const sanitized = { ...data };
  
  loggerConfig.sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  // Sanitizar nested objects
  Object.keys(sanitized).forEach(key => {
    if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      sanitized[key] = sanitizeLogData(sanitized[key]);
    }
  });
  
  return sanitized;
};

/**
 * Formata mensagem de log
 */
const formatLogMessage = (level, message, meta = {}) => {
  const timestamp = new Date().toISOString();
  const sanitizedMeta = sanitizeLogData(meta);
  
  return {
    timestamp,
    level: Object.keys(LOG_LEVELS)[level],
    message,
    meta: sanitizedMeta,
    url: window.location.href,
    userAgent: navigator.userAgent.substring(0, 100) // Limitar tamanho
  };
};

/**
 * Envia logs para serviço remoto (implementação futura)
 */
const sendLogsToRemote = async (logs) => {
  if (!config.isProduction || !logs.length) return;
  
  try {
    // TODO: Implementar envio para serviço de logging (Sentry, LogRocket, etc.)
  } catch (error) {

  }
};

/**
 * Adiciona log ao buffer
 */
const addToBuffer = (logEntry) => {
  logBuffer.push(logEntry);
  
  // Manter apenas os logs mais recentes
  if (logBuffer.length > loggerConfig.maxLogSize) {
    logBuffer = logBuffer.slice(-loggerConfig.maxLogSize);
  }
  
  // Enviar logs em batch a cada 50 entradas ou a cada 5 minutos
  if (logBuffer.length >= 50) {
    sendLogsToRemote([...logBuffer]);
    logBuffer = [];
  }
};

/**
 * Função principal de logging
 */
const log = (level, message, meta = {}) => {
  // Verificar se deve logar baseado no nível
  if (level > loggerConfig.level) return;
  
  const logEntry = formatLogMessage(level, message, meta);
  
  // Log no console se habilitado
  if (loggerConfig.enableConsole) {
    const consoleMethod = level === LOG_LEVELS.ERROR ? 'error' :
                         level === LOG_LEVELS.WARN ? 'warn' :
                         level === LOG_LEVELS.INFO ? 'info' : 'log';
    
    console[consoleMethod](`[${logEntry.level}] ${message}`, logEntry.meta);
  }
  
  // Adicionar ao buffer para envio remoto
  if (loggerConfig.enableRemote) {
    addToBuffer(logEntry);
  }
};

/**
 * Interface pública do logger
 */
export const logger = {
  error: (message, meta) => log(LOG_LEVELS.ERROR, message, meta),
  warn: (message, meta) => log(LOG_LEVELS.WARN, message, meta),
  info: (message, meta) => log(LOG_LEVELS.INFO, message, meta),
  debug: (message, meta) => log(LOG_LEVELS.DEBUG, message, meta),
  
  // Métodos utilitários
  setLevel: (level) => {
    loggerConfig.level = level;
    logger.info('Log level alterado', { newLevel: Object.keys(LOG_LEVELS)[level] });
  },
  
  getBuffer: () => [...logBuffer],
  
  clearBuffer: () => {
    logBuffer = [];
    logger.info('Buffer de logs limpo');
  },
  
  // Flush manual dos logs
  flush: () => {
    if (logBuffer.length > 0) {
      sendLogsToRemote([...logBuffer]);
      logBuffer = [];
    }
  }
};

// Configurar flush automático a cada 5 minutos
if (config.isProduction) {
  setInterval(() => {
    logger.flush();
  }, 5 * 60 * 1000);
}

// Flush logs antes de sair da página
window.addEventListener('beforeunload', () => {
  logger.flush();
});

// Log de inicialização
logger.info('Logger inicializado', {
  level: Object.keys(LOG_LEVELS)[loggerConfig.level],
  environment: config.stage,
  enableRemote: loggerConfig.enableRemote
});

export default logger;
