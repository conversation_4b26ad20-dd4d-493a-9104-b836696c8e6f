module.exports = {
  verbose: true,
  testEnvironment: 'jsdom',
  testTimeout: 30000,

  preset: 'ts-jest',
  transform: {
    '^.+\\.(ts|tsx)?$': ['ts-jest', {
      tsconfig: {
        jsx: 'react-jsx',
        target: 'ES2022',
        lib: ['ES2022', 'DOM', 'DOM.Iterable'],
        allowJs: true,
        skipLibCheck: true,
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
        strict: true,
        forceConsistentCasingInFileNames: true,
        moduleResolution: 'node',
        resolveJsonModule: true,
        isolatedModules: true,
        noEmit: true
      }
    }],
    '^.+\\.(js|jsx)$': ['babel-jest', {
      presets: [
        ['@babel/preset-env', { targets: { node: 'current' } }],
        ['@babel/preset-react', { runtime: 'automatic' }]
      ]
    }],
  },

  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^jose$': '<rootDir>/src/__mocks__/jose.js',
    '^../utils/api$': '<rootDir>/src/__mocks__/api.js',
    '^../../utils/api$': '<rootDir>/src/__mocks__/api.js',
    '^../../../utils/api$': '<rootDir>/src/__mocks__/api.js',
    '^../../../../utils/api$': '<rootDir>/src/__mocks__/api.js',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': 'jest-transform-stub'
  },

  setupFiles: ['<rootDir>/jest.polyfills.js'],
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}'
  ],

  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.js',
    '!src/reportWebVitals.js',
    '!src/setupTests.js'
  ],

  testEnvironmentOptions: {
    url: 'http://localhost'
  },

  transformIgnorePatterns: [
    'node_modules/(?!(axios|@ant-design|antd|rc-.*|@rc-component)/)'
  ],

  globals: {
    'ts-jest': {
      useESM: true
    }
  }
};
