import { useEffect } from "react";
import {
  BrowserRouter,
  Route,
  Routes,
  Navigate,
  useNavigate,
} from "react-router-dom";

//importando as páginas
import { Login } from "../pages/Login";
import { Billing } from "../pages/Billing";
import { Clients } from "../pages/Clients";
import { Audit } from "../pages/Audit";
import { Invoices } from "../pages/Invoices";
import { Contracts } from "../pages/Contracts";
import { ManageUser } from "../pages/ManageUser";
import { MFA } from "../pages/MFA";
import { SecurityMFA } from "../pages/SecurityMFA";
import { SwitchRoles } from "../pages/SwitchRoles";
import { PermissionSets } from "../pages/PermissionSets";
import { ManagePermission } from "../pages/ManagePermission";
import packageJson from "../../package.json";
import { Home } from "../pages/Home";
import { Unauthorized } from "../pages/Unauthorized";
import { TicketsAnswer } from "../pages/TicketsAnswer";
import { ServicesCatalog } from "../pages/ServicesCatalog";
import { Service } from "../pages/Service";
import { ArticlesTickets } from "../pages/ArticlesTickets";
import { Tickets } from "../pages/Tickets";
import {
  TechnicalProposalList,
  TechnicalProposalListContext,
} from "../pages/TechnicalProposalList";
import { TechnicalProposal } from "../pages/TechnicalProposal";
import { ManagmentServices } from "../pages/ManagementServices";
import { ManageFunctions } from "../pages/ManageFunctions";
import { CommercialProposal } from "../pages/CommercialProposal";
import { CommercialProposalTemplate } from "../pages/CommercialProposalTemplate";
import { CommercialProposalList } from "../pages/CommercialProposalList";
import { CostManagement } from "../pages/CostManagement";
import { AddService } from "../pages/AddService";
import {
  TechnicalProposalRfs,
  TechnicalProposalAdd,
} from "../pages/TechnicalProposalAdd";
import { CommercialProposalRfs } from "../pages/CommercialProposalAdd";
import { Logoff } from "../pages/Logoff";
import { TechnicalServiceEdit } from "../pages/TechnicalServiceEdit";
import { EditManagmentServices } from "../pages/EditManagementServices";
import IdleTimer from "../service/logoutTimer";
import { verifyExpTime } from "../service/verifyExpTime";
import { authService } from "../services/authService";
import { Cockpit } from "../pages/Cockpit";
import { CockpitPdms } from "../pages/CockpitPdms";
import { CockpitAnalista } from "../pages/CockpitAnalista";
import { Activities } from "../pages/Activities";
import { Files } from "../pages/Files";

import { ConsumptionHoursReport } from "../pages/Reports/ConsumptionHours";
import { SwitchRolesAudits } from "../pages/SwitchRoleAudit";
import { TicketsReport } from "../pages/Reports/Tickets";

function VerifyCache({ children }) {
  let version = localStorage.getItem("version");
  if (version !== packageJson.version) {
    if ("caches" in window) {
      caches.keys().then((names) => {
        names.forEach((name) => {
          caches.delete(name);
        });
      });

      window.location.reload();
    }

    localStorage.clear();
    localStorage.setItem("version", packageJson.version);
  }

  return children;
}

function RequireAuth({ children }) {
  const navigate = useNavigate();

  useEffect(() => {
    const verifySession = async () => {
      const token = authService.getToken();

      if (!token) {
        navigate("/logoff", { state: "logoff" });
        return;
      }

      const data = await verifyExpTime(token);
      return data;
    };

    verifySession().then((res) => {
      if (res === true) {
        navigate("/logoff", { state: "logoff" });
      }
    });

    // const timer = new IdleTimer({
    //   timeout: 28800,
    //   onTimeout: () => {
    //     navigate('/logoff', { state: 'logoff' })
    //   },
    //   onExpired: () => {
    //     navigate('/logoff', { state: 'logoff' })
    //   }
    // })

    // return () => {
    //   timer.cleanUp()
    // }
  }, []);

  let version = localStorage.getItem("version");

  if (version !== packageJson.version) {
    if ("caches" in window) {
      caches.keys().then((names) => {
        names.forEach((name) => {
          caches.delete(name);
        });
      });

      window.location.reload();
    }

    localStorage.clear();
    localStorage.setItem("version", packageJson.version);
  }

  const dsmName = localStorage.getItem("@dsm/name");

  if (["", null, undefined].includes(dsmName) === true) {
    return <Navigate to={"/login"} />;
  }

  return children;
}

function Router() {
  if (localStorage.getItem("@dsm/permission") === "none") {
    localStorage.clear();
    return <Navigate to={"/login"} />;
  }

  return (
    <BrowserRouter>
      <Routes>
        <Route
          path="/cockpit"
          element={
            <RequireAuth>
              <Cockpit />
            </RequireAuth>
          }
        />
        <Route
          path="/cockpit-pdms"
          element={
            <RequireAuth>
              <CockpitPdms />
            </RequireAuth>
          }
        />
        <Route
          path="/cockpit-analista"
          element={
            <RequireAuth>
              <CockpitAnalista />
            </RequireAuth>
          }
        />
        <Route
          path="/atividades"
          element={
            <RequireAuth>
              <Activities />
            </RequireAuth>
          }
        />
        <Route
          path="/login"
          element={
            <VerifyCache>
              <Login />
            </VerifyCache>
          }
        />
        <Route path="/mfa" element={<MFA />} />
        <Route path="/unauthorized" element={<Unauthorized />} />
        <Route
          path="/clients"
          element={
            <RequireAuth>
              <Clients />
            </RequireAuth>
          }
        />
        <Route
          path="/billing"
          element={
            <RequireAuth>
              <Billing />
            </RequireAuth>
          }
        />
        <Route
          path="/audit"
          element={
            <RequireAuth>
              <Audit />
            </RequireAuth>
          }
        />
        <Route
          path="/invoices"
          element={
            <RequireAuth>
              <Invoices />
            </RequireAuth>
          }
        />
        <Route
          path="/contracts"
          element={
            <RequireAuth>
              <Contracts />
            </RequireAuth>
          }
        />
        <Route
          path="/gerenciar/usuarios"
          element={
            <RequireAuth>
              <ManageUser />
            </RequireAuth>
          }
        />
        <Route
          path="/seguranca/mfa"
          element={
            <RequireAuth>
              <SecurityMFA />
            </RequireAuth>
          }
        />
        <Route
          path="/seguranca/permission-sets"
          element={
            <RequireAuth>
              <PermissionSets />
            </RequireAuth>
          }
        />
        <Route
          path="/seguranca/acessos"
          element={
            <RequireAuth>
              <SwitchRoles />
            </RequireAuth>
          }
        />
        <Route
          path="/seguranca/auditoria"
          element={
            <RequireAuth>
              <SwitchRolesAudits />
            </RequireAuth>
          }
        />
        <Route
          path="*"
          element={
            <RequireAuth>
              <Home />
            </RequireAuth>
          }
        />
        <Route path="/logoff" element={<Logoff />} />
        <Route
          path="/gerenciar/permissions"
          element={
            <RequireAuth>
              <ManagePermission />
            </RequireAuth>
          }
        />
        <Route
          path="/gerenciar/functions"
          element={
            <RequireAuth>
              <ManageFunctions />
            </RequireAuth>
          }
        />
        <Route
          path="/ticket"
          element={
            <RequireAuth>
              <TicketsAnswer />
            </RequireAuth>
          }
        />
        <Route
          path="/services"
          element={
            <RequireAuth>
              <ServicesCatalog />
            </RequireAuth>
          }
        />
        <Route
          path="/services/add"
          element={
            <RequireAuth>
              <AddService />
            </RequireAuth>
          }
        />
        <Route
          path="/services/edit"
          element={
            <RequireAuth>
              <Service />
            </RequireAuth>
          }
        />
        <Route
          path="/technical-proposals-services/edit"
          element={
            <RequireAuth>
              <TechnicalServiceEdit />
            </RequireAuth>
          }
        />
        <Route
          path="/service-management"
          element={
            <RequireAuth>
              <ManagmentServices />
            </RequireAuth>
          }
        />
        <Route
          path="/service-management/edit"
          element={
            <RequireAuth>
              <EditManagmentServices />
            </RequireAuth>
          }
        />
        <Route
          path="/technical-proposals"
          element={
            <RequireAuth>
              <TechnicalProposalList />
            </RequireAuth>
          }
        />
        <Route
          path="/technical-proposals/add"
          element={
            <RequireAuth>
              <TechnicalProposalAdd />
            </RequireAuth>
          }
        />
        <Route
          path="/technical-proposals/template/:templateID"
          element={
            <RequireAuth>
              <TechnicalProposalAdd />
            </RequireAuth>
          }
        />

        <Route
          path="/technical-proposals/edit/:proposalID"
          element={
            <RequireAuth>
              <TechnicalProposalAdd />
            </RequireAuth>
          }
        />
        <Route
          path="/commercial-proposal"
          element={
            <RequireAuth>
              <CommercialProposalList />
            </RequireAuth>
          }
        />
        <Route
          path="/commercial-proposals/add"
          element={
            <RequireAuth>
              <CommercialProposalRfs />
            </RequireAuth>
          }
        />
        <Route
          path="/commercial-proposals/edit"
          element={
            <RequireAuth>
              <CommercialProposal />
            </RequireAuth>
          }
        />
        <Route
          path="/commercial-proposals/template"
          element={
            <RequireAuth>
              <CommercialProposalTemplate />
            </RequireAuth>
          }
        />
        <Route
          path="/cost-management"
          element={
            <RequireAuth>
              <CostManagement />
            </RequireAuth>
          }
        />
        <Route
          path="/tickets"
          element={
            <RequireAuth>
              <Tickets />
            </RequireAuth>
          }
        />
        <Route
          path="/articles"
          element={
            <RequireAuth>
              <ArticlesTickets />
            </RequireAuth>
          }
        />
        <Route
          path="/ticket-answer"
          element={
            <RequireAuth>
              <TicketsAnswer />
            </RequireAuth>
          }
        />
        <Route
          path="/reports/consumption-hours"
          element={
            <RequireAuth>
              <ConsumptionHoursReport />
            </RequireAuth>
          }
        />
        <Route
          path="/files"
          element={
            <RequireAuth>
              <Files />
            </RequireAuth>
          }
        />
        <Route
          path="/reports/tickets"
          element={
            <RequireAuth>
              <TicketsReport />
            </RequireAuth>
          }
        />
      </Routes>
    </BrowserRouter>
  );
}

export default Router;
