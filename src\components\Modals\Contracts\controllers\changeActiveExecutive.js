import { message } from "antd";
import { dynamoPut } from "../../../../service/apiDsmDynamo";
import { getExecutiveNameFromEmail } from "../../../../utils/getExecutiveNameFromEmail";
import { logNewAuditAction } from "../../../../controllers/audit/logNewAuditAction";

export const changeExecutiveStatus = async (data, contract) => {
  let contractCopy = JSON.parse(JSON.stringify(contract));
  const isActive = data.active === 1 ? true : false;

  try {
    contractCopy["executives"] = contract["executives"].flatMap((executive) =>
      executive.id === data.id
        ? { ...executive, active: isActive ? 0 : 1 }
        : executive
    );
    await dynamoPut(
      `${process.env.REACT_APP_STAGE}-contracts`,
      contractCopy.id,
      contractCopy
    );

    if (!isActive) {
      message.success("Executivo ativado com sucesso");
    } else {
      message.success("Executivo desativado com sucesso");
    }

    const username = localStorage.getItem("@dsm/username");
    const title = isActive ? "Executivo Desativado" : "Executivo Ativado";
    const executiveName = await getExecutiveNameFromEmail(data.email);
    const description = `${username} ${
      isActive ? "desativou" : "ativou"
    } o executivo ${executiveName} do contrato ${contractCopy.name} com ID ${
      contractCopy.identifications.itsm_id
    }.`;

    logNewAuditAction(username, title, description);
  } catch (err) {
    console.log(err);
    message.error("Erro ao tentar alterar o status do executivo");
  }
  return contractCopy;
};
