/**
 * Configuração específica para desenvolvimento
 * Contorna problemas de CORS usando proxy local
 */

export const getApiUrl = () => {
  // SEMPRE usar URL real da API para autenticação HttpOnly
  // Nunca usar proxy local que causa problemas com cookies
  return process.env.REACT_APP_API_PERMISSION?.replace(/\/$/, '') || 'https://api.dsm.darede.com.br/dev';
};

export const getCognitoUrl = () => {
  // SEMPRE usar URL real do Cognito
  return process.env.REACT_APP_COGNITO_PARSE?.replace(/\/$/, '') || 'https://lukr7ocjz4.execute-api.us-east-1.amazonaws.com/dev/local';
};

export const getDevConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  return {
    isDevelopment,
    apiUrl: getApiUrl(),
    cognitoUrl: getCognitoUrl(),
    useProxy: false, // NUNCA usar proxy para autenticação HttpOnly
    corsEnabled: true, // CORS sempre habilitado para APIs reais
    withCredentials: true, // SEMPRE true para cookies HttpOnly
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  };
};

export const createDevAxiosConfig = (url) => {
  const config = getDevConfig();

  return {
    baseURL: url || config.apiUrl,
    withCredentials: config.withCredentials, // true para cookies HttpOnly
    timeout: config.timeout,
    headers: config.headers
  };
};

export const logDevInfo = () => {
  const config = getDevConfig();
  console.log('🔧 Configuração de desenvolvimento:', {
    apiUrl: config.apiUrl,
    cognitoUrl: config.cognitoUrl,
    note: 'SEMPRE usando URLs reais para autenticação HttpOnly'
  });
};

export default {
  getApiUrl,
  getCognitoUrl,
  getDevConfig,
  createDevAxiosConfig,
  logDevInfo
};
