/**
 * Configuração específica para desenvolvimento
 * Contorna problemas de CORS usando proxy local
 */

export const getApiUrl = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  // TEMPORÁRIO: Usar proxy em desenvolvimento devido a problemas de CORS no backend
  // Quando CORS for corrigido no backend, voltar para URL real
  if (isDevelopment) {
    console.log('🔧 Usando proxy temporário devido a problemas de CORS no backend');
    return '/api';
  }

  // Em produção, usar URL real
  return process.env.REACT_APP_API_PERMISSION?.replace(/\/$/, '') || 'https://api.dsm.darede.com.br/dev';
};

export const getCognitoUrl = () => {
  // SEMPRE usar URL real do Cognito
  return process.env.REACT_APP_COGNITO_PARSE?.replace(/\/$/, '') || 'https://lukr7ocjz4.execute-api.us-east-1.amazonaws.com/dev/local';
};

export const getDevConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  return {
    isDevelopment,
    apiUrl: getApiUrl(),
    cognitoUrl: getCognitoUrl(),
    useProxy: isDevelopment, // TEMPORÁRIO: Usar proxy em dev devido a CORS
    corsEnabled: !isDevelopment, // CORS apenas em produção
    withCredentials: !isDevelopment, // Cookies apenas em produção (proxy não suporta)
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  };
};

export const createDevAxiosConfig = (url) => {
  const config = getDevConfig();

  return {
    baseURL: url || config.apiUrl,
    withCredentials: config.withCredentials, // true para cookies HttpOnly
    timeout: config.timeout,
    headers: config.headers
  };
};

export const logDevInfo = () => {
  const config = getDevConfig();
  console.log('🔧 Configuração de desenvolvimento:', {
    apiUrl: config.apiUrl,
    cognitoUrl: config.cognitoUrl,
    note: 'SEMPRE usando URLs reais para autenticação HttpOnly'
  });
};

export default {
  getApiUrl,
  getCognitoUrl,
  getDevConfig,
  createDevAxiosConfig,
  logDevInfo
};
