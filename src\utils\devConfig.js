/**
 * Configuração específica para desenvolvimento
 * Contorna problemas de CORS usando proxy local
 */

export const getApiUrl = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    // Em desenvolvimento, usar proxy local para evitar CORS
    return '/api';
  }
  
  // Em produção, usar URL real
  return process.env.REACT_APP_API_PERMISSION?.replace(/\/$/, '');
};

export const getCognitoUrl = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    // Em desenvolvimento, usar proxy local para evitar CORS
    return '/cognito-api';
  }
  
  // Em produção, usar URL real
  return process.env.REACT_APP_COGNITO_PARSE?.replace(/\/$/, '');
};

export const getDevConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return {
    isDevelopment,
    apiUrl: getApiUrl(),
    cognitoUrl: getCognitoUrl(),
    useProxy: isDevelopment,
    corsEnabled: !isDevelopment,
    withCredentials: false, // Sempre false para evitar problemas de CORS
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  };
};

export const createDevAxiosConfig = (url) => {
  const config = getDevConfig();
  
  return {
    baseURL: url || config.apiUrl,
    withCredentials: config.withCredentials,
    timeout: config.timeout,
    headers: config.headers
  };
};

export const logDevInfo = () => {
  const config = getDevConfig();
};

export default {
  getApiUrl,
  getCognitoUrl,
  getDevConfig,
  createDevAxiosConfig,
  logDevInfo
};
