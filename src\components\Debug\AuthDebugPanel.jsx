/**
 * Painel de debug para autenticação HttpOnly
 * Componente para testar e verificar o sistema de autenticação
 */

import React, { useState } from 'react';
import { Button, Card, Space, Typography, Alert, Collapse, Tag } from 'antd';
import { 
  BugOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  LoadingOutlined,
  ApiOutlined,
  <PERSON>ieOutlined,
  SecurityScanOutlined
} from '@ant-design/icons';
import { runAllAuthTests, debugRequest } from '../../utils/authTestUtils';
import { config } from '../../utils/validateEnvironment';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

export const AuthDebugPanel = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState(null);
  const [summary, setSummary] = useState(null);

  const runTests = async () => {
    setIsRunning(true);
    try {
      const { results, summary } = await runAllAuthTests();
      setResults(results);
      setSummary(summary);
    } catch (error) {
      console.error('Erro ao executar testes:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const testSpecificEndpoint = async (endpoint) => {
    try {
      await debugRequest(`${config.apiUrl}${endpoint}`);
    } catch (error) {
      // Erro já logado no debugRequest
    }
  };

  const getStatusIcon = (status) => {
    if (status === true) return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    if (status === false) return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
    return <LoadingOutlined />;
  };

  const getStatusColor = (status) => {
    if (status === true) return 'success';
    if (status === false) return 'error';
    return 'processing';
  };

  return (
    <Card 
      title={
        <Space>
          <BugOutlined />
          <Title level={4} style={{ margin: 0 }}>
            Painel de Debug - Autenticação HttpOnly
          </Title>
        </Space>
      }
      style={{ margin: '20px 0' }}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        
        {/* Informações do Sistema */}
        <Card size="small" title="Configurações do Sistema">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text><strong>API URL:</strong> {config.apiUrl}</Text>
            <Text><strong>Stage:</strong> {config.stage}</Text>
            <Text><strong>Environment:</strong> {process.env.NODE_ENV}</Text>
            <Text><strong>Cognito URL:</strong> {config.cognitoUrl}</Text>
          </Space>
        </Card>

        {/* Botões de Teste */}
        <Space wrap>
          <Button 
            type="primary" 
            icon={<SecurityScanOutlined />}
            loading={isRunning}
            onClick={runTests}
          >
            Executar Todos os Testes
          </Button>
          
          <Button 
            icon={<ApiOutlined />}
            onClick={() => testSpecificEndpoint('/auth/verify')}
          >
            Testar /auth/verify
          </Button>
          
          <Button 
            icon={<CookieOutlined />}
            onClick={() => testSpecificEndpoint('/cognito/read')}
          >
            Testar /cognito/read
          </Button>
        </Space>

        {/* Resultados dos Testes */}
        {summary && (
          <Alert
            message="Resumo dos Testes"
            description={
              <Space direction="vertical" style={{ width: '100%' }}>
                <Space wrap>
                  <Tag color={getStatusColor(summary.apiAccessible)} icon={getStatusIcon(summary.apiAccessible)}>
                    API Acessível
                  </Tag>
                  <Tag color={getStatusColor(summary.authWorking)} icon={getStatusIcon(summary.authWorking)}>
                    Autenticação HttpOnly
                  </Tag>
                  <Tag color={getStatusColor(summary.cognitoWorking)} icon={getStatusIcon(summary.cognitoWorking)}>
                    Cognito Read
                  </Tag>
                  <Tag color={getStatusColor(summary.securityOk)} icon={getStatusIcon(summary.securityOk)}>
                    Segurança OK
                  </Tag>
                  <Tag color={getStatusColor(summary.interceptorsConfigured)} icon={getStatusIcon(summary.interceptorsConfigured)}>
                    Interceptors
                  </Tag>
                </Space>
                
                {summary.apiAccessible && summary.authWorking && summary.cognitoWorking && summary.securityOk ? (
                  <Text type="success">🎉 Sistema funcionando corretamente!</Text>
                ) : (
                  <Text type="danger">❌ Alguns problemas detectados - verificar logs do console</Text>
                )}
              </Space>
            }
            type={summary.apiAccessible && summary.authWorking && summary.cognitoWorking && summary.securityOk ? 'success' : 'warning'}
            showIcon
          />
        )}

        {/* Detalhes dos Resultados */}
        {results && (
          <Collapse>
            <Panel header="Detalhes dos Testes" key="details">
              <Space direction="vertical" style={{ width: '100%' }}>
                
                <Card size="small" title="Conectividade da API">
                  <Text>Status: {getStatusIcon(results.apiConnectivity)} {results.apiConnectivity ? 'Conectado' : 'Falha na conexão'}</Text>
                </Card>

                <Card size="small" title="Configurações do Axios">
                  <Space direction="vertical">
                    <Text>Interceptors de Request: {results.axiosConfig.interceptors.request}</Text>
                    <Text>Interceptors de Response: {results.axiosConfig.interceptors.response}</Text>
                    <Text>WithCredentials Global: {String(results.axiosConfig.defaults.withCredentials)}</Text>
                    <Text>Timeout: {results.axiosConfig.defaults.timeout}ms</Text>
                  </Space>
                </Card>

                <Card size="small" title="Cookies e Segurança">
                  <Space direction="vertical">
                    <Text>Cookies Visíveis: {Object.keys(results.browserCookies.visibleCookies).length}</Text>
                    <Text>Tokens no localStorage: {results.browserCookies.localStorageTokens.length}</Text>
                    {results.browserCookies.localStorageTokens.length > 0 && (
                      <Alert 
                        message="Tokens inseguros detectados!" 
                        description={`Tokens encontrados no localStorage: ${results.browserCookies.localStorageTokens.join(', ')}`}
                        type="warning" 
                        size="small"
                      />
                    )}
                  </Space>
                </Card>

                <Card size="small" title="Autenticação HttpOnly">
                  <Space direction="vertical">
                    <Text>Status: {getStatusIcon(!!results.httpOnlyAuth)} {results.httpOnlyAuth ? 'Funcionando' : 'Falha'}</Text>
                    {results.httpOnlyAuth && (
                      <Text>Usuário: {results.httpOnlyAuth.user?.email || 'N/A'}</Text>
                    )}
                  </Space>
                </Card>

                <Card size="small" title="Cognito Read">
                  <Space direction="vertical">
                    <Text>Status: {getStatusIcon(!!results.cognitoRead)} {results.cognitoRead ? 'Funcionando' : 'Falha'}</Text>
                    {results.cognitoRead && (
                      <Text>Usuários encontrados: {results.cognitoRead.data?.length || 0}</Text>
                    )}
                  </Space>
                </Card>

              </Space>
            </Panel>
          </Collapse>
        )}

        {/* Instruções */}
        <Card size="small" title="Como usar este painel">
          <Paragraph>
            <ol>
              <li><strong>Execute todos os testes</strong> para verificar o status geral do sistema</li>
              <li><strong>Verifique os logs do console</strong> para detalhes técnicos</li>
              <li><strong>Teste endpoints específicos</strong> se algum teste falhar</li>
              <li><strong>Certifique-se de estar autenticado</strong> antes de testar endpoints protegidos</li>
            </ol>
          </Paragraph>
          
          <Alert
            message="Nota Importante"
            description="Este painel é apenas para desenvolvimento. Remova em produção."
            type="info"
            size="small"
          />
        </Card>

      </Space>
    </Card>
  );
};

export default AuthDebugPanel;
