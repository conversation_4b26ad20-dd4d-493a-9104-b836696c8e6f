const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'https://api.dsm.darede.com.br/dev',
      changeOrigin: true,
      pathRewrite: {
        '^/api': '', // Remove /api do path
      },
      onProxyReq: (proxyReq, req, res) => {
        console.log('🔧 Proxy Request:', req.method, req.url, '→', proxyReq.path);
        
        // Log cookies sendo enviados
        if (req.headers.cookie) {
          console.log('🍪 Cookies enviados:', req.headers.cookie);
        }
      },
      onProxyRes: (proxyRes, req, res) => {
        console.log('✅ Proxy Response:', proxyRes.statusCode, req.url);
      },
      onError: (err, req, res) => {
        console.error('❌ Proxy Error:', err.message, req.url);
      }
    })
  );
};
