const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'https://api.dsm.darede.com.br/dev',
      changeOrigin: true,
      pathRewrite: {
        '^/api': '', // Remove /api do path
      },
      onProxyReq: (proxyReq, req, res) => {
        console.log('🔧 Proxy Request:', req.method, req.url, '→', proxyReq.path);

        // Log cookies sendo enviados
        if (req.headers.cookie) {
          console.log('🍪 Cookies enviados:', req.headers.cookie);

          // Verificar se dsm_access_token está presente
          const hasDsmToken = req.headers.cookie.includes('dsm_access_token=');
          console.log('🎯 dsm_access_token presente:', hasDsmToken);

          if (hasDsmToken) {
            // Extrair apenas o token para verificar se está bem formado
            const tokenMatch = req.headers.cookie.match(/dsm_access_token=([^;]+)/);
            if (tokenMatch) {
              const token = tokenMatch[1];
              console.log('🔍 Token extraído:', {
                length: token.length,
                starts: token.substring(0, 20) + '...',
                isJWT: token.includes('.') && token.split('.').length === 3
              });
            }
          }
        } else {
          console.log('❌ Nenhum cookie enviado');
        }
      },
      onProxyRes: (proxyRes, req, res) => {
        console.log('✅ Proxy Response:', proxyRes.statusCode, req.url);
      },
      onError: (err, req, res) => {
        console.error('❌ Proxy Error:', err.message, req.url);
      }
    })
  );
};
