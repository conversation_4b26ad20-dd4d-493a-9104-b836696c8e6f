import React, {useState} from "react";
import {Card, Popover, Row, Spin, Table, Typography} from 'antd';
import {FilterFilled} from '@ant-design/icons';
import {TechnicianFilters} from "../../Modals/Filters/TechnicianFilter";
import {jiraGet} from "../../../service/apiJira";
import 'antd/dist/antd.css';
import "./index.css";

export const Technician = () => {
    const { Title, Text } = Typography;
    const [data, setData] = useState([]);
    const [popoverVisibility, setPopoverVisibility] = useState(false);
    const [loading, setLoading] = useState(false);

    async function getTableData(filters) {
        const {professional, project} = filters;
        let payloadArray = [];

        if (professional) {
            setLoading(true);

            const {data} = await jiraGet(`read/user/issues/${professional}`);
            const {issues} = data;


            for (let issue of issues) {
                const {key, fields} = issue;

                const {data} = await jiraGet(`read/worklogs/${key}`);
                const {worklogs} = data;

                for (let worklog of worklogs) {
                    const {updateAuthor, timeSpentSeconds} = worklog;

                    if (professional === updateAuthor.accountId) {
                        const payloadItem = payloadArray.find((item) => item.user === updateAuthor.displayName && item.project === fields.project.name);

                        if (!payloadItem) {
                            payloadArray.push({
                                user: updateAuthor.displayName,
                                project: fields.project.name,
                                registeredTime: timeSpentSeconds,
                                engagedTime: 0
                            });
                        } else {
                            payloadItem.registeredTime += timeSpentSeconds;
                        }
                    }
                }
            }

            if (project) {
                payloadArray = payloadArray.filter((item) => item.project === project);
            }

            for (let item of payloadArray) {
                const {project} = item;
                const {data} = await jiraGet(`read/project/issues/${project}`);
                const {issues} = data;

                for (let issue of issues) {
                    const {fields} = issue;

                    if (fields.timeoriginalestimate) {
                        item.engagedTime += fields.timeoriginalestimate;
                    }
                }
            }
        }

        setData(payloadArray);
        setLoading(false);
    }

    function handlePopoverVisibility() {
        setPopoverVisibility(!popoverVisibility);
    }

    function formatName(name) {
        const words = name.toLowerCase().split(" ");

        return words.map((word) => word[0].toUpperCase() + word.substring(1)).join(" ");
    }

    const columns = [
        {
            title: 'Profissional',
            dataIndex: 'user',
            key: 'user',
            render: (user) => <p className="table-font">{formatName(user)}</p>,
        },
        {
            title: 'Projeto',
            dataIndex: 'project',
            key: 'project',
            render: (project) => <p className="table-font">{project}</p>,
        },
        {
            title: 'Registradas',
            dataIndex: 'registeredTime',
            key: 'registeredTime',
            render: (registeredTime) => <p className="table-font">{(registeredTime / 3600).toFixed(2)} horas</p>
        },
        {
            title: 'Contratadas',
            dataIndex: 'engagedTime',
            key: 'engagedTime',
            // sorter: (a, b) => a.contractedHours - b.contractedHours,
            render: (engagedTime) => <p className="table-font">{(engagedTime / 3600).toFixed(2)} horas</p>,
        },
    ];

    return (
        <Card bordered={false} style={{height: '100%', borderRadius: '20px', boxShadow: "0 0 10px rgba(0,0,0,0.1)"}}>
            <Row justify="space-between">
                <Title level={4} style={{fontWeight: 400}}>Técnicos</Title>
                    {loading ? (
                        <Row justify='center' align='middle'>
                            <Spin />
                        </Row>
                    ) : (
                        ''
                    )}
                <div>
                    <Popover
                        visible={popoverVisibility}
                        onClick={handlePopoverVisibility}
                        placement="left"
                        content={() => {
                            return (
                                <TechnicianFilters
                                    handlePopoverVisibility={handlePopoverVisibility}
                                    formatName={formatName}
                                    filter={getTableData}
                                />
                            )
                        }}
                        trigger="click">
                        <div className="table-filter-content">
                            <FilterFilled className="table-filter-icon"/>
                        </div>
                    </Popover>
                </div>
            </Row>
            <div className="table-padding">
                <Table scroll={{x: '100%'}} columns={columns} dataSource={data}/>
            </div>
        </Card>
    )
}