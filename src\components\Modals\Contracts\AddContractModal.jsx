import {
  Modal,
  Button,
  Input,
  Select,
  DatePicker,
  Form,
  message,
  Typography,
} from "antd";
import React, { useState } from "react";
import { format, differenceInCalendarMonths } from "date-fns";
import {
  createContract,
  dynamoPost,
  dynamoPut,
  useDynamoGet,
} from "../../../service/apiDsmDynamo";
import { otrsPost } from "../../../service/apiOtrs";
import { RequiredLabelForm } from "../../RequiredLabelForm";

import * as controller from "../../../controllers/contracts/contract-controller";
import { updateConsumptionHourContract } from "../../../provider/otrs-calculate-hours-provider";
import { formatCreateContractForm } from "./controllers/addContract";

export const AddContractModal = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loadingClients, setLoadingClients] = useState(false);
  const [loading, setLoading] = useState(false);
  const clients = useDynamoGet(`${process.env.REACT_APP_STAGE}-customers`);
  const { RangePicker } = DatePicker;
  const [form] = Form.useForm();
  const { Option } = Select;

  if (!clients.data && !loadingClients) {
    return setLoadingClients(true);
  }

  if (clients.data && loadingClients) {
    return setLoadingClients(false);
  }

  const handleSubmit = (data) => {
    setLoading(true);

    const diff = differenceInCalendarMonths(
      new Date(data.expected_start_date[1]._d),
      new Date(data.expected_start_date[0]._d)
    );

    try {
      otrsPost("create/contract", {
        name: data.name,
        customer_id: JSON.parse(data.customer).itsm_id,
        start_date: format(
          new Date(data.expected_start_date[0]._d),
          "yyyy/MM/dd"
        ),
        end_date: format(
          new Date(data.expected_start_date[1]._d),
          "yyyy/MM/dd"
        ),
        duration: diff,
        total_hours: parseInt(data.total_hours),
        type_hours: data.type_hours,
        document_id: null,
        sla_id: null,
        type_id: null,
        priority_id: null,
        notify_email: null,
        notify_percentage: null,
        freeze_percentage: null,
      })
        .then(async (response) => {
          try {
            await createContract(
              formatCreateContractForm(data, diff, response)
            );
            if (response.data) {
              updateConsumptionHourContract({
                contractID: response.data.contract_id,
              });
              await dynamoPut(
                `${process.env.REACT_APP_STAGE}-customers`,
                JSON.parse(data.customer).id,
                {
                  active: 1,
                  has_active_contracts: 1,
                }
              );
            }

            const username = localStorage.getItem("@dsm/username");

            const title = "Contrato criado";
            const description = `${username} criou o contrato ${data.name}.`;

            dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
              username: username,
              name: title,
              description: description,
              created_at: new Date(),
              updated_at: new Date(),
            });

            controller.getActiveContracts();

            message.success("Sucesso na criação do contrato!");

            setLoading(false);
            handleCancel();
          } catch (err) {
            setLoading(false);
            message.error("Já existe um contrato com esse nome");
          }
        })
        .catch((err) => {
          setLoading(false);
          message.error("Erro na criação do contrato");
        });
    } catch (error) {
      return setLoading(false);
    }
  };

  const showModal = async () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModalVisible(false);
  };

  return (
    <>
      <Button type="primary" onClick={showModal}>
        Cadastrar Contrato
      </Button>
      <Modal
        title="Cadastre um Contrato"
        open={isModalVisible}
        onOk={handleOk}
        okText="Adicionar Contrato"
        cancelText="Cancelar"
        okButtonProps={{ loading: loading }}
        onCancel={handleCancel}
      >
        <Form
          requiredMark={false}
          layout="vertical"
          onFinish={handleSubmit}
          form={form}
        >
          <Form.Item name="crm_id" label="CRM ID">
            <Input placeholder="CRM ID" />
          </Form.Item>
          <Form.Item
            label={<RequiredLabelForm title="Nome do Contrato" />}
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="name"
          >
            <Input placeholder="Nome do Contrato" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="value"
            label={<RequiredLabelForm title="Valor do Contrato" />}
          >
            <Input placeholder="Valor do Contrato" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="type_hours"
            label={<RequiredLabelForm title="Tipo de Consumo" />}
          >
            <Select
              placeholder="Selecione o Tipo de Consumo"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children?.toLowerCase().indexOf(input?.toLowerCase()) >=
                0
              }
            >
              <Option value="Mensal">Mensal</Option>
              <Option value="Trimestral">Trimestral</Option>
              <Option value="Semestral">Semestral</Option>
              <Option value="Anual">Anual</Option>
              <Option value="Projeto">Projeto</Option>
              <Option value="Ondemand">OnDemand</Option>
              <Option value="Ilimitado">Ilimitado</Option>
            </Select>
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="expected_start_date"
            label={<RequiredLabelForm title="Duração do Contrato" />}
          >
            <RangePicker
              style={{ width: "100%" }}
              placeholder={["Início do Contrato", "Fim do Contrato"]}
            />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="total_hours"
            label={<RequiredLabelForm title="Total de Horas" />}
          >
            <Input placeholder="Total de Horas" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="pool_type"
            label={<RequiredLabelForm title="Tipo de horas" />}
          >
            <Select placeholder="Selecione o tipo de Horas...">
              <Option value="pool_8_5">Pool 8x5</Option>
              <Option value="pool_24_7">Pool 24x7</Option>
              <Option value="pool_setup_8_5">Pool Setup 8x5</Option>
              <Option value="pool_setup_24_7">Pool Setup 24x7</Option>
            </Select>
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="customer"
            label={<RequiredLabelForm title="Cliente" />}
          >
            <Select
              placeholder="Selecione um cliente"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children?.toLowerCase().indexOf(input?.toLowerCase()) >=
                0
              }
              loading={loadingClients}
            >
              {clients.data
                ?.filter(
                  (i) =>
                    i?.identifications?.itsm_id &&
                    (i?.names?.fantasy_name || i?.names?.name)
                )
                .map((e, index) => {
                  return (
                    <Option
                      key={index}
                      value={JSON.stringify({
                        id: e?.id,
                        itsm_id: e.identifications?.itsm_id,
                      })}
                    >
                      {e.names?.name
                        ? e.names.name + " - " + e.names.fantasy_name
                        : e.names.fantasy_name}
                    </Option>
                  );
                })}
            </Select>
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            label={<RequiredLabelForm title="Valor/Hora" />}
            name="excess_cost"
          >
            <Input placeholder="Valor/Hora" />
          </Form.Item>
          <Form.Item label="Calculadora AWS" name="aws_value">
            <Input placeholder="Valor da Calculadora AWS" />
          </Form.Item>
          <Form.Item label="Link da Calculadora AWS" name="aws_calculator_link">
            <Input placeholder="Link da Calculadora AWS" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            label={<RequiredLabelForm title="Escopo" />}
            name="scope"
          >
            <Input.TextArea placeholder="Insira o escopo do contrato" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
