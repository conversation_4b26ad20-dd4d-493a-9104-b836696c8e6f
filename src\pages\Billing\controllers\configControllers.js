import { message } from "antd";
import { dynamoPut } from "../../../service/apiDsmDynamo";
import { configBillingProvider } from "../../../provider/config-billing-provider";

export const configBilling = async (payerAccount, contract_id) => {
  try {
    const provider = configBillingProvider();
    const response = await provider.post(`/s3/configure/${payerAccount}`);
    await dynamoPut(`${process.env.REACT_APP_STAGE}-contracts`, contract_id, {
      billingConfigured: true,
      payer_account: payerAccount,
    });
    return response.status;
  } catch (error) {
    console.log(error);
    message.error("Erro ao tentar configurar Billing.");
  }
};

export const resetBilling = async (payerAccount, contract_id) => {
  try {
    const provider = configBillingProvider();
    const response = await provider.post(`/s3/reset/${payerAccount}`);
    await dynamoPut(`${process.env.REACT_APP_STAGE}-contracts`, contract_id, {
      billingConfigured: false,
      payer_account: null,
    });
    return response.status;
  } catch (error) {
    console.log(error);
    message.error("Erro ao tentar desconfigurar Billing.");
  }
};
