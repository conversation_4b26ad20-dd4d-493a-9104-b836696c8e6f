/**
 * Hook useAuth melhorado para Cognito + Cookies HttpOnly
 * Fornece funcionalidades avançadas de autenticação
 */

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useCognitoAuth } from '../contexts/CognitoAuthProvider';
import { logger } from '../utils/logger';
import { AUTH_ROUTES, AUTH_STATUS } from '../constants/auth';

/**
 * Hook principal de autenticação
 */
export const useAuth = () => {
  const context = useCognitoAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  const [lastActivity, setLastActivity] = useState(Date.now());
  const [sessionWarning, setSessionWarning] = useState(false);

  /**
   * Atualizar última atividade
   */
  const updateActivity = useCallback(() => {
    setLastActivity(Date.now());
    setSessionWarning(false);
  }, []);

  /**
   * Login com redirecionamento automático
   */
  const loginWithRedirect = useCallback(async (code, redirectTo) => {
    try {
      const result = await context.login(code);
      
      // Redirecionar para página desejada ou home
      const targetPath = redirectTo || 
                        location.state?.from || 
                        AUTH_ROUTES.HOME;
      
      navigate(targetPath, { replace: true });
      
      return result;
    } catch (error) {
      logger.error('Erro no login com redirecionamento', { error: error.message });
      throw error;
    }
  }, [context, navigate, location.state]);

  /**
   * Logout com redirecionamento
   */
  const logoutWithRedirect = useCallback(async () => {
    try {
      await context.logout();
      navigate(AUTH_ROUTES.LOGOUT, { replace: true });
    } catch (error) {
      logger.error('Erro no logout com redirecionamento', { error: error.message });
      // Mesmo com erro, redirecionar
      navigate(AUTH_ROUTES.LOGOUT, { replace: true });
    }
  }, [context, navigate]);

  /**
   * Verificar se precisa de autenticação
   */
  const requireAuth = useCallback((redirectTo = AUTH_ROUTES.LOGIN) => {
    if (!context.isAuthenticated && !context.isLoading) {
      navigate(redirectTo, { 
        state: { from: location.pathname },
        replace: true 
      });
      return false;
    }
    return true;
  }, [context.isAuthenticated, context.isLoading, navigate, location.pathname]);

  /**
   * Verificar permissão com redirecionamento
   */
  const requirePermission = useCallback((permission, redirectTo = AUTH_ROUTES.UNAUTHORIZED) => {
    if (!context.isAuthenticated) {
      return requireAuth();
    }
    
    if (!context.hasPermission(permission)) {
      navigate(redirectTo, { replace: true });
      return false;
    }
    
    return true;
  }, [context.isAuthenticated, context.hasPermission, requireAuth, navigate]);

  /**
   * Verificar múltiplas permissões
   */
  const requireAnyPermission = useCallback((permissions = [], redirectTo = AUTH_ROUTES.UNAUTHORIZED) => {
    if (!context.isAuthenticated) {
      return requireAuth();
    }
    
    if (!context.hasAnyPermission(permissions)) {
      navigate(redirectTo, { replace: true });
      return false;
    }
    
    return true;
  }, [context.isAuthenticated, context.hasAnyPermission, requireAuth, navigate]);

  /**
   * Monitorar atividade do usuário
   */
  useEffect(() => {
    if (!context.isAuthenticated) return;

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      updateActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [context.isAuthenticated, updateActivity]);

  /**
   * Verificar inatividade (opcional)
   */
  useEffect(() => {
    if (!context.isAuthenticated) return;

    const inactivityTimeout = 25 * 60 * 1000; // 25 minutos
    const warningTimeout = 23 * 60 * 1000; // 23 minutos

    const checkInactivity = () => {
      const now = Date.now();
      const timeSinceActivity = now - lastActivity;

      if (timeSinceActivity >= inactivityTimeout) {
        logger.warn('Sessão expirada por inatividade');
        logoutWithRedirect();
      } else if (timeSinceActivity >= warningTimeout && !sessionWarning) {
        setSessionWarning(true);
        logger.warn('Aviso de inatividade');
      }
    };

    const interval = setInterval(checkInactivity, 60000); // Verificar a cada minuto

    return () => clearInterval(interval);
  }, [context.isAuthenticated, lastActivity, sessionWarning, logoutWithRedirect]);

  /**
   * Helpers memoizados
   */
  const authHelpers = useMemo(() => ({
    isLoggedIn: context.isAuthenticated,
    isLoggedOut: !context.isAuthenticated && !context.isLoading,
    isInitializing: context.isLoading,
    
    currentUser: context.user,
    userDisplayName: context.userName || context.userEmail,
    userAvatar: context.userInitials,
    
    // Permission helpers
    canAccess: context.hasPermission,
    canAccessAny: context.hasAnyPermission,
    canAccessAll: context.hasAllPermissions,
    
    // Session helpers
    sessionActive: context.isAuthenticated && !sessionWarning,
    sessionExpiring: sessionWarning,
    lastUserActivity: lastActivity,
    
    // Method helpers
    authMethod: context.authMethod,
    isHttpOnlyAuth: context.authMethod === 'httponly',
    isCognitoAuth: context.authMethod === 'cognito',
    isManualAuth: context.authMethod === 'manual'
  }), [
    context.isAuthenticated,
    context.isLoading,
    context.user,
    context.userName,
    context.userEmail,
    context.userInitials,
    context.hasPermission,
    context.hasAnyPermission,
    context.hasAllPermissions,
    context.authMethod,
    sessionWarning,
    lastActivity
  ]);

  /**
   * Métodos de conveniência
   */
  const authMethods = useMemo(() => ({
    login: context.login,
    loginWithRedirect,
    redirectToLogin: context.redirectToLogin,
    
    logout: context.logout,
    logoutWithRedirect,
    
    checkAuth: context.checkAuth,
    refreshToken: context.refreshToken,
    
    requireAuth,
    requirePermission,
    requireAnyPermission,
    
    updateActivity,
    extendSession: updateActivity
  }), [
    context.login,
    context.logout,
    context.checkAuth,
    context.refreshToken,
    context.redirectToLogin,
    loginWithRedirect,
    logoutWithRedirect,
    requireAuth,
    requirePermission,
    requireAnyPermission,
    updateActivity
  ]);

  // Retornar API completa
  return {
    // Estados do contexto
    ...context,
    
    // Helpers
    ...authHelpers,
    
    // Métodos
    ...authMethods,
    
    // Estados locais
    sessionWarning,
    lastActivity
  };
};

/**
 * Hook para verificação rápida de autenticação
 */
export const useAuthStatus = () => {
  const { isAuthenticated, isLoading, status } = useAuth();
  
  return useMemo(() => ({
    isAuthenticated,
    isLoading,
    isUnauthenticated: !isAuthenticated && !isLoading,
    status
  }), [isAuthenticated, isLoading, status]);
};

/**
 * Hook para verificação de permissões
 */
export const usePermissions = (requiredPermissions = []) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, user } = useAuth();
  
  return useMemo(() => ({
    hasPermission,
    hasAny: hasAnyPermission(requiredPermissions),
    hasAll: hasAllPermissions(requiredPermissions),
    userPermissions: user?.permissions || [],
    missing: requiredPermissions.filter(p => !hasPermission(p))
  }), [hasPermission, hasAnyPermission, hasAllPermissions, user, requiredPermissions]);
};

/**
 * Hook para guards de rota
 */
export const useRouteGuard = () => {
  const { requireAuth, requirePermission, requireAnyPermission } = useAuth();
  
  return {
    requireAuth,
    requirePermission,
    requireAnyPermission
  };
};

/**
 * Hook para informações do usuário
 */
export const useCurrentUser = () => {
  const { 
    user, 
    userEmail, 
    userName, 
    userInitials,
    userDisplayName 
  } = useAuth();
  
  return useMemo(() => ({
    user,
    email: userEmail,
    name: userName,
    displayName: userDisplayName,
    initials: userInitials,
    isLoaded: !!user
  }), [user, userEmail, userName, userDisplayName, userInitials]);
};

export default useAuth;
