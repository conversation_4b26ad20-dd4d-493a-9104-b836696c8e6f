/**
 * Cliente API configurado para cookies HttpOnly
 * Configuração otimizada para o novo backend
 */

import axios from 'axios';
import { logger } from '../utils/logger';
import { config } from '../utils/validateEnvironment';
import { httpOnlyAuthService } from './httpOnlyAuthService';

// Instância principal da API com configuração CORS padronizada
const apiClient = axios.create({
  baseURL: config.apiUrl,
  withCredentials: false, // PADRONIZADO: Controlado pelos interceptors
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// REMOVIDO: Request interceptor local
// A configuração de autenticação agora é gerenciada centralmente
// pelo httpOnlyAuthService através dos interceptors globais

// Mantém apenas log em desenvolvimento se necessário
if (process.env.NODE_ENV === 'development') {
  apiClient.interceptors.request.use(
    (config) => {
      logger.debug('API Request', {
        method: config.method?.toUpperCase(),
        url: config.url,
        withCredentials: config.withCredentials
      });
      return config;
    },
    (error) => {
      logger.error('Erro no request interceptor', { error: error.message });
      return Promise.reject(error);
    }
  );
}

// REMOVIDO: Response interceptor local
// O auto-refresh e tratamento de erros agora é gerenciado centralmente
// pelo httpOnlyAuthService através dos interceptors globais

// Mantém apenas log em desenvolvimento se necessário
if (process.env.NODE_ENV === 'development') {
  apiClient.interceptors.response.use(
    (response) => {
      logger.debug('API Response', {
        status: response.status,
        url: response.config?.url
      });
      return response;
    },
    (error) => {
      // Log apenas, sem tratamento (feito pelos interceptors globais)
      logger.debug('API Error', {
        status: error.response?.status,
        message: error.message,
        url: error.config?.url
      });
      return Promise.reject(error);
    }
  );
}

// Cliente para APIs externas (sem cookies)
const externalApiClient = axios.create({
  withCredentials: false,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Cliente para Cognito (sem cookies)
const cognitoClient = axios.create({
  baseURL: config.cognitoUrl,
  withCredentials: false,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Interceptor para Cognito
cognitoClient.interceptors.request.use(
  (config) => {
    logger.debug('Cognito Request', {
      method: config.method?.toUpperCase(),
      url: config.url
    });
    return config;
  }
);

cognitoClient.interceptors.response.use(
  (response) => {
    logger.debug('Cognito Response', {
      status: response.status
    });
    return response;
  },
  (error) => {
    logger.error('Erro no Cognito', {
      status: error.response?.status,
      message: error.message
    });
    return Promise.reject(error);
  }
);

/**
 * Funções utilitárias para diferentes tipos de requisições
 */

// Requisições autenticadas (com cookies)
export const authenticatedRequest = {
  get: (url, config = {}) => apiClient.get(url, config),
  post: (url, data, config = {}) => apiClient.post(url, data, config),
  put: (url, data, config = {}) => apiClient.put(url, data, config),
  patch: (url, data, config = {}) => apiClient.patch(url, data, config),
  delete: (url, config = {}) => apiClient.delete(url, config)
};

// Requisições para Cognito
export const cognitoRequest = {
  get: (url, config = {}) => cognitoClient.get(url, config),
  post: (url, data, config = {}) => cognitoClient.post(url, data, config)
};

// Requisições externas (sem cookies)
export const externalRequest = {
  get: (url, config = {}) => externalApiClient.get(url, config),
  post: (url, data, config = {}) => externalApiClient.post(url, data, config)
};

/**
 * Funções específicas para autenticação
 */
export const authAPI = {
  // Definir token em cookies HttpOnly
  setToken: (token) => apiClient.post('/auth/set-token', { token }),
  
  // Renovar token
  refresh: () => apiClient.post('/auth/refresh'),
  
  // Verificar autenticação
  verify: () => apiClient.get('/auth/verify'),
  
  // Fazer logout
  logout: () => apiClient.post('/auth/logout')
};

/**
 * Teste de conectividade
 */
export const testConnection = async () => {
  try {
    const response = await apiClient.get('/health', { timeout: 5000 });
    logger.info('Conexão com API estabelecida', {
      status: response.status
    });
    return true;
  } catch (error) {
    logger.warn('Falha na conexão com API', {
      error: error.message
    });
    return false;
  }
};

// Exportações principais
export { apiClient as default, externalApiClient, cognitoClient };
export { apiClient };
