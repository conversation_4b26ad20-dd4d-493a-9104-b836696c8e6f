import Axios from "axios";
import useSWR from "swr";
import { authService } from "../services/authService";
import { getApiUrl } from "../utils/devConfig";

export const generateToken = async () => {
  const { data } = await Axios.get(
    process.env.REACT_APP_API_BASE_URL + "get/token"
  );

  return data.data;
};

export function useDSMGet(route) {
  const { data, error, mutate } = useSWR(
    process.env.REACT_APP_API_BASE_URL + route,
    async () => {
      const token = await generateToken();

      const { data } = await Axios.get(
        process.env.REACT_APP_API_BASE_URL + route,
        {
          headers: { Authorization: token },
        }
      );

      return data.data;
    }
  );

  return { data, error, mutate };
}

export const dsmGet = async (route) => {
  try {
    const apiUrl = getApiUrl();
    const data = await Axios.get(`${apiUrl}/${route}`);
    return data.data.Items;
  } catch (e) {
    console.log(e);
    return [];
  }
};

export const dsmPost = async (route, body) => {
  const token = await generateToken();

  const { data } = await Axios.post(
    process.env.REACT_APP_API_BASE_URL + route,
    { ...body },
    {
      headers: { Authorization: token },
    }
  );

  return data;
};

export const dsmPut = async (route, body) => {
  const token = await generateToken();

  const { data } = await Axios.put(
    process.env.REACT_APP_API_BASE_URL + route,
    { ...body },
    {
      headers: { Authorization: token },
    }
  );

  return data;
};

export const dsmDelete = async (route, body) => {
  const token = await generateToken();

  const { data } = await Axios.delete(
    process.env.REACT_APP_API_BASE_URL + route,
    {
      headers: { Authorization: token },
    }
  );

  return data;
};
