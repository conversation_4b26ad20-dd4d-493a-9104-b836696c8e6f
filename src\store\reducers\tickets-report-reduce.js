import { createSlice } from "@reduxjs/toolkit";
import moment from "moment";

const REDUCER_NAME = "ticketsReport";

export const allOption = { value: "Todos", label: "Todos" };

export const INITIAL_STATE = {
  customers: [],
  customerSelected: null,
  contracts: [allOption],
  contractSelected: allOption,
  tickets: [],
  search: "",
  filteredTickets: [],
  month: moment(),
};

const ticketReportSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setTicketReportReduce(state, action) {
      state[action.payload.field] = action.payload.value;
    },
  },
});

export const { setTicketReportReduce } = ticketReportSlice.actions;

export default ticketReportSlice.reducer;
