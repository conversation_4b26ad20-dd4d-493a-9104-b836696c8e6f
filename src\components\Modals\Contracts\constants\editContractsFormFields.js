import React from "react";
import { yesOrNoOptions } from "../../../../constants/yesOrNoOptions";
import { Select } from "../../../Select";
import * as contractsFormOptions from "./contractsFormSelectOptions";
import { ExportOutlined } from "@ant-design/icons";

const addonSelectCurrency = (
  <Select
    defaultValue="N/A"
    options={contractsFormOptions.currency}
    placeholder="N/A"
  ></Select>
);

export const editContractsFormFields = [
  {
    label: "CRM",
    permissionCode: "edit_contract_crm",
    key: "crm_id",
    inputType: "text",
    dataType: "text",
    required: false,
    tabKey: "general_info",
    placeholder: "CRM ID",
    formIndex: "identifications crm_id",
  },
  {
    label: "Id do contrato",
    permissionCode: "edit_contract_itsm_id",
    key: "itsm_id",
    inputType: "text",
    dataType: "number",
    required: true,
    tabKey: "general_info",
    placeholder: "ITSM ID",
    formIndex: "identifications itsm_id",
  },
  {
    label: "Nome do contrato",
    permissionCode: "edit_contract_name",
    key: "name",
    inputType: "text",
    dataType: "string",
    required: true,
    tabKey: "general_info",
    placeholder: "Nome do contrato",
  },
  {
    label: "Data de início da vigência",
    permissionCode: "edit_starting_date",
    key: "expected_start_date",
    inputType: "date",
    dataType: "string",
    required: true,
    tabKey: "general_info",
    placeholder: "Início do contrato",
    formIndex: "target_dates expected_start_date",
  },
  {
    label: "Data de fim da vigência",
    permissionCode: "edit_ending_date",
    key: "expected_close_date",
    inputType: "date",
    dataType: "string",
    required: true,
    tabKey: "general_info",
    placeholder: "Fim do contrato",
    formIndex: "target_dates expected_close_date",
  },
  {
    label: "Tipo de consumo",
    permissionCode: "edit_contract_type_hours",
    key: "type_hours",
    inputType: "select",
    dataType: "string",
    required: true,
    tabKey: "general_info",
    placeholder: "Tipo de consumo",
    options: contractsFormOptions.consumedType,
  },
  {
    label: "Tipo de horas",
    permissionCode: "edit_contract_pool_type",
    key: "pool_type",
    inputType: "select",
    dataType: "string",
    required: true,
    tabKey: "general_info",
    placeholder: "Tipo de horas",
    options: contractsFormOptions.typeHours,
  },
  {
    label: "Squad",
    permissionCode: "edit_contract_squad",
    key: "squad",
    inputType: "select",
    dataType: "string",
    required: true,
    tabKey: "general_info",
    placeholder: "Squad",
  },
  {
    label: "Prazo do contrato",
    permissionCode: "edit_contract_contract_deadline",
    key: "duration",
    inputType: "select",
    dataType: "string",
    required: true,
    tabKey: "general_info",
    placeholder: "Prazo contratual",
    options: contractsFormOptions.contractDuration,
  },
  {
    label: "Escopo",
    permissionCode: "edit_contract_scope",
    key: "scope",
    inputType: "textArea",
    dataType: "string",
    required: true,
    tabKey: "general_info",
    placeholder: "Escopo",
  },
  {
    label: "Valor do contrato",
    permissionCode: "edit_contract_value",
    key: "value",
    inputType: "text",
    dataType: "number",
    required: true,
    tabKey: "financial_info",
    placeholder: "Valor do contrato",
    formIndex: "values value",
    addonChildren: addonSelectCurrency,
  },
  {
    label: "Valor Previsto",
    permissionCode: "edit_expected_value",
    key: "expected_value",
    inputType: "text",
    dataType: "number",
    required: false,
    tabKey: "financial_info",
    placeholder: "Valor Previsto",
  },
  {
    label: "Valor/Hora",
    permissionCode: "edit_contract_value_per_hour",
    key: "excess_cost",
    inputType: "text",
    dataType: "number",
    required: true,
    tabKey: "financial_info",
    placeholder: "Valor/Hora",
  },
  {
    label: "Link da Proposta",
    permissionCode: "edit_s3_folder_notes",
    key: "s3FolderNotes",
    inputType: "text",
    dataType: "link",
    required: false,
    tabKey: "financial_info",
    placeholder: "Link da Proposta",
    AddonIcon: ExportOutlined,
  },
  {
    label: "Valor da Calculadora AWS",
    permissionCode: "edit_contract_aws_calculator",
    key: "aws_value",
    inputType: "text",
    dataType: "link",
    required: false,
    tabKey: "financial_info",
    placeholder: "Valor da Calculadora AWS",
    formIndex: "values aws_value",
  },
  {
    label: "Link da Calculadora AWS",
    permissionCode: "edit_contract_aws_value",
    key: "aws_calculator_link",
    inputType: "text",
    dataType: "url",
    required: false,
    tabKey: "financial_info",
    placeholder: "Valor AWS",
  },
  {
    label: "Porcentagem de bloqueio",
    permissionCode: "edit_contract_block_percentage",
    key: "block",
    inputType: "text",
    dataType: "number",
    required: false,
    tabKey: "financial_info",
    placeholder: "Porcentagem de bloqueio",
    formIndex: "percentage block",
  },
  {
    label: "Porcentagem de notificação",
    permissionCode: "edit_contract_freezing_percentage",
    key: "freezing",
    inputType: "text",
    dataType: "number",
    required: false,
    tabKey: "financial_info",
    placeholder: "Porcentagem de notificação",
    formIndex: "percentage freezing",
  },
  {
    label: "E-mails de notificação",
    permissionCode: "edit_contract_notification_emails",
    key: "emails",
    inputType: "text",
    dataType: "string",
    required: false,
    tabKey: "financial_info",
    placeholder: "Adicione separados por ;",
  },
  {
    label: "E-mails de bloqueio",
    permissionCode: "edit_contract_block_emails",
    key: "block_emails",
    inputType: "text",
    dataType: "string",
    required: false,
    tabKey: "financial_info",
    placeholder: "Adicione separados por ;",
  },
  {
    label: "Valor faturado",
    permissionCode: "edit_contract_billed_value",
    key: "billed_value",
    inputType: "text",
    dataType: "number",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Valor faturado",
  },
  {
    label: "Índice de reajuste",
    permissionCode: "edit_contract_reajustment_index",
    key: "reajustment_index",
    inputType: "select",
    dataType: "string",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Índice de Reajuste",
    options: contractsFormOptions.readjustmentPercentage,
  },
  {
    label: "Percentual Reajustado",
    permissionCode: "edit_contract_reajustment_percentage",
    key: "reajustment_percentage",
    inputType: "text",
    dataType: "number",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Percentual Reajustado",
  },
  {
    label: "Valor/Hora Excedente",
    permissionCode: "edit_contract_excess_hour_value",
    key: "excess_hour_value",
    inputType: "text",
    dataType: "number",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Valor Hora Excedente",
  },
  {
    label: "Renovação Automática",
    permissionCode: "edit_contract_automatic_renewal",
    key: "automatic_renewal",
    inputType: "select",
    dataType: "string",
    required: true,
    tabKey: "contracts_info",
    placeholder: "Renovação Automática",
    options: yesOrNoOptions,
  },
  {
    label: "Assinado",
    permissionCode: "edit_contract_signed",
    key: "signed",
    inputType: "select",
    dataType: "string",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Assinado",
    options: yesOrNoOptions,
  },
  {
    label: "Data de Assinatura",
    permissionCode: "edit_contract_signature_date",
    key: "signature_date",
    inputType: "date",
    dataType: "string",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Data de Assinatura",
  },
  {
    label: "Multa Rescisão",
    permissionCode: "edit_contract_rescission_fine",
    key: "rescission_fine",
    inputType: "select",
    dataType: "string",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Multa Rescisão",
    options: yesOrNoOptions,
  },
  {
    label: "Multa SLA",
    permissionCode: "edit_contract_sla_fine",
    key: "sla_fine",
    inputType: "select",
    dataType: "string",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Multa SLA",
    options: yesOrNoOptions,
  },
  {
    label: "Data de Cancelamento/Rescisão",
    permissionCode: "edit_contract_cancellation_date",
    key: "cancellation_date",
    inputType: "date",
    dataType: "string",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Data de Cancelamento/Rescisão",
  },
  {
    label: "Rescisão Aviso Prévio",
    permissionCode: "edit_contract_notice_rescission",
    key: "notice_rescission",
    inputType: "text",
    dataType: "number",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Rescisão Aviso Prévio",
  },
  {
    label: "Tipo de contrato",
    permissionCode: "edit_contract_discount",
    key: "discount",
    inputType: "select",
    dataType: "string",
    required: false,
    tabKey: "contracts_info",
    placeholder: "Tipo de contrato",
    options: [
      {
        value: "edp",
        label: "EDP",
      },
      {
        value: "spp",
        label: "SPP",
      },
    ],
  },
  {
    label: "Commitment EDP",
    permissionCode: "edit_contract_edp_commitment",
    key: "edp_commitment",
    hidden: true,
    inputType: "date",
    dataType: "string",
    required: false,
    tabKey: "contracts_info",
    placeholder: "mês/ano",
    subItems: [{ commitmentKey: 0 }],
  },
  {
    label: "Invoice",
    permissionCode: "edit_contract_invoice",
    key: "invoice",
    inputType: "text",
    dataType: "number",
    required: false,
    tabKey: "governance_info",
    placeholder: "Digite o número da invoice",
  },
  {
    label: "Ordem de Compra",
    permissionCode: "edit_contract_purchase_order",
    key: "purchase_order",
    inputType: "text",
    dataType: "string",
    required: false,
    tabKey: "governance_info",
    placeholder: "Digite o número da ordem de compra",
  },
  {
    label: "Invoice Amount",
    permissionCode: "edit_contract_invoice_amount",
    key: "invoice_amount",
    inputType: "text",
    dataType: "number",
    required: false,
    tabKey: "governance_info",
    placeholder: "Invoice Amount",
  },
  {
    label: "Invoice Status",
    permissionCode: "edit_contract_invoice_status",
    key: "invoice_status",
    inputType: "text",
    dataType: "string",
    required: false,
    tabKey: "governance_info",
    placeholder: "Invoice Status",
  },
  {
    label: "Período de reajuste",
    permissionCode: "edit_contract_reajustment_period",
    key: "reajustment_period",
    inputType: "text",
    dataType: "string",
    required: false,
    tabKey: "governance_info",
    placeholder: "Período de reajuste",
  },
  {
    label: "Target",
    permissionCode: "edit_contract_target",
    key: "target",
    inputType: "text",
    dataType: "string",
    required: false,
    tabKey: "governance_info",
    placeholder: "Target",
  },
  {
    label: "Período performado",
    permissionCode: "edit_contract_performed_period",
    key: "performed_period",
    inputType: "text",
    dataType: "string",
    required: false,
    tabKey: "governance_info",
    placeholder: "Período performado",
  },
  {
    label: "Data inicial de serviço",
    permissionCode: "edit_contract_service_start_date",
    key: "service_start_date",
    inputType: "date",
    dataType: "date",
    required: false,
    tabKey: "governance_info",
    placeholder: "Data inicial de serviço",
  },
  {
    label: "Contrato",
    permissionCode: "edit_contract_contract_bond",
    key: "contract_bond",
    inputType: "select",
    dataType: "string",
    required: false,
    tabKey: "governance_info",
    placeholder: "Vínculo contratual",
    options: contractsFormOptions.contractBond,
  },
];

export const fullWidthFields = [
  "scope",
  "block_emails",
  "emails",
  "aws_calculator_link",
  "name",
  "edp_commitment",
];

export const editContractModalTabs = [
  {
    title: "Informações gerais",
    key: "general_info",
    permissionCode: "edit_modal_view_tab_general_info",
  },
  {
    title: "Informações financeiras",
    key: "financial_info",
    permissionCode: "edit_modal_view_tab_financial_info",
  },
  {
    title: "Informações time de contratos",
    key: "contracts_info",
    permissionCode: "edit_modal_view_tab_contracts_info",
  },
  {
    title: "Informações times Governança/Financeiro",
    key: "governance_info",
    permissionCode: "edit_modal_view_tab_governance_info",
  },
];
