import React, { createContext, useContext, useEffect, useState } from 'react';
import { defaultCache, smartCache, persistentCache, sessionCache } from '../services/cacheService';

/**
 * Contexto para gerenciamento de cache
 * Fornece acesso aos diferentes tipos de cache e estatísticas
 */
const CacheContext = createContext(null);

/**
 * Provider do contexto de cache
 */
export const CacheProvider = ({ children }) => {
  const [stats, setStats] = useState({
    memory: { size: 0, entries: [] },
    persistent: { size: 0, entries: [] },
    session: { size: 0, entries: [] }
  });

  const [isOnline, setIsOnline] = useState(navigator.onLine);

  /**
   * Atualiza estatísticas dos caches
   */
  const updateStats = () => {
    setStats({
      memory: defaultCache.getStats(),
      persistent: persistentCache.getStats(),
      session: sessionCache.getStats()
    });
  };

  /**
   * Limpa todos os caches
   */
  const clearAllCaches = () => {
    defaultCache.clear();
    smartCache.clear();
    persistentCache.clear();
    sessionCache.clear();
    updateStats();
  };

  /**
   * Limpa cache específico
   */
  const clearCache = (type) => {
    switch (type) {
      case 'memory':
        defaultCache.clear();
        smartCache.clear();
        break;
      case 'persistent':
        persistentCache.clear();
        break;
      case 'session':
        sessionCache.clear();
        break;
      default:
        console.warn('Tipo de cache inválido:', type);
    }
    updateStats();
  };

  /**
   * Executa limpeza de caches expirados
   */
  const cleanup = () => {
    const results = {
      memory: defaultCache.cleanup() + smartCache.cleanup(),
      persistent: persistentCache.cleanup(),
      session: sessionCache.cleanup()
    };
    
    updateStats();
    return results;
  };

  /**
   * Obtém cache por tipo
   */
  const getCache = (type) => {
    switch (type) {
      case 'memory':
        return defaultCache;
      case 'smart':
        return smartCache;
      case 'persistent':
        return persistentCache;
      case 'session':
        return sessionCache;
      default:
        return defaultCache;
    }
  };

  /**
   * Invalida cache por padrão de chave
   */
  const invalidatePattern = (pattern, cacheType = 'all') => {
    const caches = cacheType === 'all' 
      ? [defaultCache, smartCache, persistentCache, sessionCache]
      : [getCache(cacheType)];

    caches.forEach(cache => {
      const stats = cache.getStats();
      stats.entries.forEach(entry => {
        if (entry.key.includes(pattern)) {
          cache.delete(entry.key);
        }
      });
    });

    updateStats();
  };

  /**
   * Configura estratégias de cache baseadas na conectividade
   */
  const setupOfflineStrategy = () => {
    if (!isOnline) {
      // Quando offline, usar mais cache persistente
      return {
        defaultTTL: 3600000, // 1 hora
        preferPersistent: true
      };
    } else {
      // Quando online, usar configuração normal
      return {
        defaultTTL: 300000, // 5 minutos
        preferPersistent: false
      };
    }
  };

  /**
   * Sincroniza caches quando volta online
   */
  const syncOnReconnect = async () => {
    if (isOnline) {
      // Invalidar caches que podem estar desatualizados
      const staleKeys = [];
      
      [defaultCache, smartCache].forEach(cache => {
        const stats = cache.getStats();
        stats.entries.forEach(entry => {
          const age = Date.now() - entry.created.getTime();
          if (age > 600000) { // Mais de 10 minutos
            staleKeys.push(entry.key);
          }
        });
      });

      // Invalidar chaves antigas
      staleKeys.forEach(key => {
        defaultCache.delete(key);
        smartCache.invalidate && smartCache.invalidate(key);
      });

      updateStats();
    }
  };

  // Monitorar conectividade
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      syncOnReconnect();
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Atualizar estatísticas periodicamente
  useEffect(() => {
    updateStats();
    
    const interval = setInterval(updateStats, 30000); // A cada 30 segundos
    
    return () => clearInterval(interval);
  }, []);

  // Limpeza automática
  useEffect(() => {
    const cleanupInterval = setInterval(cleanup, 300000); // A cada 5 minutos
    
    return () => clearInterval(cleanupInterval);
  }, []);

  const value = {
    // Caches
    defaultCache,
    smartCache,
    persistentCache,
    sessionCache,
    
    // Estatísticas
    stats,
    
    // Estado
    isOnline,
    
    // Ações
    clearAllCaches,
    clearCache,
    cleanup,
    getCache,
    invalidatePattern,
    updateStats,
    
    // Estratégias
    setupOfflineStrategy,
    syncOnReconnect
  };

  return (
    <CacheContext.Provider value={value}>
      {children}
    </CacheContext.Provider>
  );
};

/**
 * Hook para usar o contexto de cache
 */
export const useCacheContext = () => {
  const context = useContext(CacheContext);
  
  if (!context) {
    throw new Error('useCacheContext deve ser usado dentro de um CacheProvider');
  }
  
  return context;
};

/**
 * Hook para estatísticas de cache
 */
export const useCacheStats = () => {
  const { stats, updateStats } = useCacheContext();
  
  const totalSize = stats.memory.size + stats.persistent.size + stats.session.size;
  const totalEntries = stats.memory.entries.length + stats.persistent.entries.length + stats.session.entries.length;
  
  return {
    stats,
    totalSize,
    totalEntries,
    updateStats,
    breakdown: {
      memory: {
        percentage: totalSize > 0 ? (stats.memory.size / totalSize) * 100 : 0,
        ...stats.memory
      },
      persistent: {
        percentage: totalSize > 0 ? (stats.persistent.size / totalSize) * 100 : 0,
        ...stats.persistent
      },
      session: {
        percentage: totalSize > 0 ? (stats.session.size / totalSize) * 100 : 0,
        ...stats.session
      }
    }
  };
};

/**
 * Hook para estratégias de cache offline
 */
export const useOfflineCache = () => {
  const { isOnline, setupOfflineStrategy, persistentCache } = useCacheContext();
  
  const strategy = setupOfflineStrategy();
  
  const cacheForOffline = (key, data, params = {}) => {
    if (!isOnline || strategy.preferPersistent) {
      persistentCache.set(key, data, strategy.defaultTTL, params);
    }
  };
  
  const getOfflineData = (key, params = {}) => {
    return persistentCache.get(key, params);
  };
  
  return {
    isOnline,
    strategy,
    cacheForOffline,
    getOfflineData
  };
};

export default CacheContext;
