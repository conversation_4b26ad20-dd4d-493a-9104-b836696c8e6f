import React, {useState, useEffect} from 'react'
import {Table, Select} from 'antd'
import {ArrowLeftOutlined} from '@ant-design/icons'
import 'antd/dist/antd.css'
import './totalHoursByCategory.css'

const columns = [
    {
        title: 'Nome do contrato',
        dataIndex: 'name',
        key: 'name',
        render: (name) => <p className="table-font">{name}</p>,
    },
    {
        title: '<PERSON><PERSON><PERSON>',
        dataIndex: 'total_hours',
        key: 'total_hours',
        render: (total_hours) => <p className="table-font">{total_hours}</p>,
    },
]

const TotalHoursByCategory = ({contractSelected, selectedClient, filteredContracts, showContracts}) => {

    useEffect(() => {
    }, [selectedClient])

    return (
        <>
            <div className="table-card-top table-totalHoursCategory">
                <div className="button">
                    <button className="filter-clear-btn button-back" onClick={() => showContracts()}>
                        <ArrowLeftOutlined/>
                    </button>
                </div>
                <h2>{contractSelected.client}</h2>
            </div>
            <div>
                <Table
                    scroll={{y: 200}}
                    pagination={false}
                    columns={columns}
                    dataSource={filteredContracts}
                >
                </Table>
            </div>
        </>
    )
}

export {TotalHoursByCategory}