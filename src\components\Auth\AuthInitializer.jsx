/**
 * Componente de Inicialização da Autenticação
 * Gerencia o processo de inicialização e configuração da autenticação
 */

import React, { useEffect, useState } from 'react';
import { Spin, Alert, Button, Card, Space, Typography, Progress } from 'antd';
import { 
  LoadingOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  ReloadOutlined 
} from '@ant-design/icons';
import { useCognitoAuth } from '../../contexts/CognitoAuthProvider';
import { logger } from '../../utils/logger';

const { Title, Text, Paragraph } = Typography;

/**
 * Estados de inicialização
 */
const INIT_STATES = {
  INITIALIZING: 'initializing',
  SUCCESS: 'success',
  ERROR: 'error',
  RETRY: 'retry'
};

/**
 * Componente de loading durante inicialização
 */
const InitializationLoader = ({ progress, message, details }) => (
  <div style={{ 
    display: 'flex', 
    flexDirection: 'column',
    alignItems: 'center', 
    justifyContent: 'center',
    minHeight: '100vh',
    padding: '24px',
    background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
  }}>
    <Card 
      style={{ 
        textAlign: 'center',
        maxWidth: '500px',
        width: '100%',
        borderRadius: '12px',
        boxShadow: '0 8px 24px rgba(0,0,0,0.1)'
      }}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Spin 
          indicator={<LoadingOutlined style={{ fontSize: 48, color: '#00B050' }} />}
          size="large"
        />
        
        <Title level={3} style={{ margin: 0, color: '#333' }}>
          Inicializando Sistema
        </Title>
        
        <Progress 
          percent={progress} 
          strokeColor="#00B050"
          showInfo={false}
          style={{ margin: '16px 0' }}
        />
        
        <Text type="secondary" style={{ fontSize: '16px' }}>
          {message}
        </Text>
        
        {details && (
          <div style={{ textAlign: 'left', width: '100%' }}>
            <Text strong>Detalhes:</Text>
            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
              {details.map((detail, index) => (
                <li key={index}>
                  <Text type="secondary" style={{ fontSize: '14px' }}>
                    {detail}
                  </Text>
                </li>
              ))}
            </ul>
          </div>
        )}
      </Space>
    </Card>
  </div>
);

/**
 * Componente de erro de inicialização
 */
const InitializationError = ({ error, onRetry, details }) => (
  <div style={{ 
    display: 'flex', 
    flexDirection: 'column',
    alignItems: 'center', 
    justifyContent: 'center',
    minHeight: '100vh',
    padding: '24px',
    background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
  }}>
    <Card 
      style={{ 
        textAlign: 'center',
        maxWidth: '600px',
        width: '100%',
        borderRadius: '12px',
        boxShadow: '0 8px 24px rgba(0,0,0,0.1)'
      }}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <ExclamationCircleOutlined 
          style={{ fontSize: 48, color: '#ff4d4f' }}
        />
        
        <Title level={3} style={{ margin: 0, color: '#333' }}>
          Erro na Inicialização
        </Title>
        
        <Alert
          message="Falha na Configuração"
          description={error}
          type="error"
          showIcon
          style={{ textAlign: 'left' }}
        />
        
        {details && details.length > 0 && (
          <Alert
            message="Detalhes Técnicos"
            description={
              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                {details.map((detail, index) => (
                  <li key={index}>{detail}</li>
                ))}
              </ul>
            }
            type="info"
            showIcon
            style={{ textAlign: 'left' }}
          />
        )}
        
        <Space>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />}
            onClick={onRetry}
            style={{ backgroundColor: '#00B050', borderColor: '#00B050' }}
          >
            Tentar Novamente
          </Button>
          
          <Button 
            onClick={() => window.location.reload()}
          >
            Recarregar Página
          </Button>
        </Space>
        
        {process.env.NODE_ENV === 'development' && (
          <Alert
            message="Modo Desenvolvimento"
            description="Verifique o console do navegador para mais detalhes sobre o erro."
            type="warning"
            showIcon
            style={{ textAlign: 'left' }}
          />
        )}
      </Space>
    </Card>
  </div>
);

/**
 * Componente principal de inicialização
 */
export const AuthInitializer = ({ children, fallback = null }) => {
  const auth = useCognitoAuth();
  const [initState, setInitState] = useState(INIT_STATES.INITIALIZING);
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('Preparando sistema...');
  const [details, setDetails] = useState([]);
  const [error, setError] = useState(null);

  /**
   * Processo de inicialização
   */
  const initialize = async () => {
    try {
      setInitState(INIT_STATES.INITIALIZING);
      setProgress(0);
      setError(null);
      setDetails([]);

      // Etapa 1: Verificar configuração do Amplify
      setMessage('Verificando configuração do AWS Amplify...');
      setDetails(['Validando User Pool ID', 'Verificando Client ID', 'Testando conectividade']);
      setProgress(20);
      
      await new Promise(resolve => setTimeout(resolve, 500)); // Simular delay
      
      if (!auth.amplifyInitialized) {
        throw new Error('AWS Amplify não foi inicializado corretamente');
      }

      // Etapa 2: Verificar suporte HttpOnly
      setMessage('Verificando suporte a cookies HttpOnly...');
      setDetails(['Testando endpoint de verificação', 'Configurando interceptors', 'Validando CORS']);
      setProgress(40);
      
      await new Promise(resolve => setTimeout(resolve, 500));

      // Etapa 3: Verificar autenticação existente
      setMessage('Verificando autenticação existente...');
      setDetails(['Verificando tokens armazenados', 'Validando sessão', 'Carregando dados do usuário']);
      setProgress(60);
      
      await new Promise(resolve => setTimeout(resolve, 500));

      // Etapa 4: Configurar interceptors
      setMessage('Configurando interceptors de requisição...');
      setDetails(['Configurando refresh automático', 'Definindo headers padrão', 'Testando conectividade']);
      setProgress(80);
      
      await new Promise(resolve => setTimeout(resolve, 500));

      // Etapa 5: Finalizar
      setMessage('Finalizando inicialização...');
      setDetails(['Sistema pronto para uso']);
      setProgress(100);
      
      await new Promise(resolve => setTimeout(resolve, 300));

      setInitState(INIT_STATES.SUCCESS);
      
      logger.info('Inicialização da autenticação concluída com sucesso', {
        amplifyInitialized: auth.amplifyInitialized,
        httpOnlySupported: auth.httpOnlySupported,
        authMethod: auth.authMethod,
        isAuthenticated: auth.isAuthenticated
      });

    } catch (error) {
      logger.error('Erro na inicialização da autenticação', { error: error.message });
      setError(error.message);
      setInitState(INIT_STATES.ERROR);
      
      // Adicionar detalhes do erro
      const errorDetails = [
        `Erro: ${error.message}`,
        `Amplify inicializado: ${auth.amplifyInitialized ? 'Sim' : 'Não'}`,
        `HttpOnly suportado: ${auth.httpOnlySupported ? 'Sim' : 'Não'}`,
        `Ambiente: ${process.env.NODE_ENV}`,
        `Stage: ${process.env.REACT_APP_STAGE || 'Não definido'}`
      ];
      setDetails(errorDetails);
    }
  };

  /**
   * Retry da inicialização
   */
  const handleRetry = () => {
    setInitState(INIT_STATES.RETRY);
    setTimeout(initialize, 1000);
  };

  /**
   * Inicializar na montagem
   */
  useEffect(() => {
    // Aguardar um pouco para garantir que o contexto está pronto
    const timer = setTimeout(initialize, 100);
    return () => clearTimeout(timer);
  }, []);

  // Renderizar baseado no estado
  switch (initState) {
    case INIT_STATES.INITIALIZING:
    case INIT_STATES.RETRY:
      return (
        <InitializationLoader 
          progress={progress}
          message={message}
          details={details}
        />
      );

    case INIT_STATES.ERROR:
      return (
        <InitializationError 
          error={error}
          onRetry={handleRetry}
          details={details}
        />
      );

    case INIT_STATES.SUCCESS:
      // Se ainda está carregando a autenticação, mostrar loading
      if (auth.isLoading) {
        return (
          <InitializationLoader 
            progress={90}
            message="Verificando autenticação..."
            details={['Carregando dados do usuário']}
          />
        );
      }
      
      // Sistema inicializado com sucesso
      return children;

    default:
      return fallback || children;
  }
};

/**
 * HOC para envolver componentes com inicialização
 */
export const withAuthInitializer = (Component) => {
  return (props) => (
    <AuthInitializer>
      <Component {...props} />
    </AuthInitializer>
  );
};

export default AuthInitializer;
