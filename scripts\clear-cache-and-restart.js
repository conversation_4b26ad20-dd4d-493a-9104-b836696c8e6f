#!/usr/bin/env node

/**
 * Script para limpar cache e reiniciar com configurações corretas
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧹 Limpando cache e reiniciando com configurações corretas...\n');

// 1. Verificar arquivos .env
console.log('📋 Verificando arquivos .env...');
const envFiles = ['.env', '.env.local', '.env.development', '.env.production'];

envFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} encontrado`);
    
    // Ler e verificar conteúdo
    const content = fs.readFileSync(file, 'utf8');
    if (content.includes('localhost:8000')) {
      console.log(`⚠️  ${file} contém localhost:8000 - DEVE ser corrigido!`);
    } else {
      console.log(`✅ ${file} não contém localhost:8000`);
    }
  }
});

// 2. Limpar cache do npm
console.log('\n🗑️  Limpando cache do npm...');
try {
  execSync('npm cache clean --force', { stdio: 'inherit' });
  console.log('✅ Cache do npm limpo');
} catch (error) {
  console.log('⚠️  Erro ao limpar cache do npm:', error.message);
}

// 3. Remover node_modules/.cache se existir
console.log('\n🗑️  Limpando cache do webpack...');
const cacheDir = path.join('node_modules', '.cache');
if (fs.existsSync(cacheDir)) {
  try {
    fs.rmSync(cacheDir, { recursive: true, force: true });
    console.log('✅ Cache do webpack removido');
  } catch (error) {
    console.log('⚠️  Erro ao remover cache do webpack:', error.message);
  }
} else {
  console.log('ℹ️  Cache do webpack não encontrado');
}

// 4. Verificar configurações importantes
console.log('\n🔍 Verificando configurações importantes...');

// Verificar devConfig.js
const devConfigPath = path.join('src', 'utils', 'devConfig.js');
if (fs.existsSync(devConfigPath)) {
  const devConfig = fs.readFileSync(devConfigPath, 'utf8');
  if (devConfig.includes("return '/api'")) {
    console.log('❌ devConfig.js ainda contém return "/api" - DEVE ser corrigido!');
  } else {
    console.log('✅ devConfig.js não contém return "/api"');
  }
  
  if (devConfig.includes('localhost')) {
    console.log('❌ devConfig.js contém localhost - DEVE ser corrigido!');
  } else {
    console.log('✅ devConfig.js não contém localhost');
  }
}

// 5. Mostrar URLs que devem ser usadas
console.log('\n🎯 URLs corretas que devem ser usadas:');
console.log('  API: https://api.dsm.darede.com.br/dev');
console.log('  Cognito: https://lukr7ocjz4.execute-api.us-east-1.amazonaws.com/dev/local');

console.log('\n❌ URLs que NÃO devem aparecer:');
console.log('  localhost:8000 (NUNCA)');
console.log('  /api (NUNCA)');
console.log('  /cognito-api (NUNCA)');

// 6. Instruções finais
console.log('\n📋 PRÓXIMOS PASSOS:');
console.log('1. Parar o servidor de desenvolvimento (Ctrl+C)');
console.log('2. Executar: npm start');
console.log('3. Verificar logs no console - devem mostrar URLs reais');
console.log('4. Testar autenticação');
console.log('5. Usar painel de debug na página Home');

console.log('\n🔧 Se ainda houver problemas:');
console.log('1. Limpar cache do browser (Ctrl+Shift+R)');
console.log('2. Verificar Network tab - URLs devem ser reais');
console.log('3. Executar: npm run test-auth');

console.log('\n✅ Limpeza concluída! Reinicie o servidor de desenvolvimento.');
