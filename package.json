{"name": "dsm-front-end", "version": "0.5.8", "private": true, "dependencies": {"@amcharts/amcharts4": "^4.10.23", "@craco/craco": "^7.1.0", "@fortawesome/fontawesome-svg-core": "^6.1.2", "@fortawesome/free-solid-svg-icons": "^6.1.2", "@fortawesome/react-fontawesome": "^0.2.0", "@reduxjs/toolkit": "^1.9.1", "@testing-library/jest-dom": "^5.15.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@tinymce/tinymce-react": "^4.1.0", "@types/moment": "^2.13.0", "alloyeditor": "^2.14.7", "amazon-cognito-identity-js": "*", "antd": "^4.21.3", "antd-mask-input": "^2.0.7", "autoprefixer": "^10.4.19", "aws-amplify": "^6.15.1", "axios": "^1.10.0", "base32-decode": "^1.0.0", "base32-encode": "^2.0.0", "chart.js": "^3.6.1", "craco-less": "^2.1.0-alpha.0", "date-fns": "^2.28.0", "dotenv": "^16.0.1", "draft-js": "^0.11.7", "draft-to-html": "0.0.1", "draftjs-to-html": "^0.9.1", "exceljs": "^4.3.0", "faker": "^5.5.3", "file-saver": "^2.0.5", "fs": "0.0.1-security", "html-to-image": "^1.10.4", "html2canvas": "^1.4.1", "http-proxy-middleware": "^2.0.6", "image-to-base64": "^2.2.0", "immer": "^9.0.16", "jodit-react": "^1.3.9", "jose": "^4.9.2", "jquery": "^3.6.0", "js-cookie": "^3.0.1", "js-file-download": "^0.4.12", "jspdf": "^2.5.1", "jwt-decode": "^3.1.2", "moment": "^2.30.1", "ms-teams-webhook": "^1.0.4", "nodemon": "^1.10.2", "pdfmake": "^0.2.5", "phosphor-react": "^1.4.1", "postcss": "^8.4.38", "qrcode": "^1.5.0", "rasterizehtml": "^1.3.1", "react": "^18.2.0", "react-chartjs-2": "^4.0.0", "react-collapsed": "^3.3.1", "react-color": "^2.19.3", "react-csv": "^2.2.1", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-google-recaptcha": "^2.1.0", "react-icons": "^4.3.1", "react-json-view": "^1.21.3", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^6.0.2", "react-scripts": "^5.0.1", "react-select": "^5.4.0", "react-toastify": "^9.1.1", "recharts": "^2.1.6", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "speakeasy": "^2.0.0", "swr": "^1.2.1", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "uuid": "^9.0.1", "web-vitals": "^1.1.2", "webpack": "^5.91.0", "webpack-bundle-analyzer": "^4.5.0", "webpackbar": "^5.0.2"}, "scripts": {"deploy-dev": "aws s3 sync ./build s3://hml.dsm.darede.com.br/ --acl public-read --profile hml-serverless-framework", "deploy-prod": "aws s3 sync ./build s3://dsm.darede.com.br/ --acl public-read --profile dosystems-serverless-framework", "start": "nodemon -w .config.js -w ./antd.customize.less --exec \"npx craco start\"", "build": "craco --max_old_space_size=4096 build", "test": "react-scripts test", "test-all": "react-scripts test --watchAll=false --coverage .", "test:watch": "react-scripts test --watch", "test-auth": "node scripts/test-auth-fix.js", "lint": "oxlint src", "lint:fix": "oxlint src --fix", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/react-csv": "^1.1.10", "aws-sdk": "^2.839.0", "axios-mock-adapter": "^1.21.4", "chalk": "^4.1.2", "jsdom": "^22.1.0", "oxlint": "^1.2.0", "react-error-overlay": "^6.0.9", "resolve-url-loader": "^5.0.0", "sass": "^1.69.5", "sass-loader": "^13.3.2"}}