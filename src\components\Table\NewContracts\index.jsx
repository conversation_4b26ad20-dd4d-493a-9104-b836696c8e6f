import React, { useEffect, useState } from "react";
import "antd/dist/antd.css";
import "../../../styles/table.css";
import { Button, Col, Popover, Row, Spin, Table } from "antd";
import { FilterFilled, CloseOutlined } from "@ant-design/icons";
import moment from "moment";
import { otrsGet } from "../../../service/apiOtrs";
import NewContractsFilter from "../../Modals/Filters/NewContractsFilter";
import { SearchInput } from "../../SearchInput";

const columns = [
  {
    title: "Cliente",
    dataIndex: "client",
    key: "client",
    render: (client) => <p className="table-font">{client}</p>,
  },
  {
    title: "Contrato",
    dataIndex: "name",
    key: "name",
    render: (name) => <p className="table-font">{name}</p>,
  },
  {
    title: "Car<PERSON>ira",
    dataIndex: "document_id",
    key: "document_id",
    render: (document_id) => <p className="table-font">{document_id}</p>,
  },
  {
    title: "Data de vencimento",
    dataIndex: "end_date",
    key: "end_date",
    render: (end_date) => <p className="table-font">{end_date}</p>,
  },
];

function NewContractsTable(props) {
  const [data, setData] = useState();
  const [loading, setLoading] = useState();
  const [filteredData, setFilteredData] = useState([]);
  const [popoverVisibility, setPopoverVisibility] = useState(false);
  const { closeModal, tableData, contractTypes } = props;

  async function generateTable() {
    setLoading(true);
    const tempData = [];
    const customers = [];

    await otrsGet(`read/customer/all/0`).then((resp) => {
      Object.entries(resp.data).forEach((customer) => {
        customers.push(customer[1]);
      });

      for (let i = 0; i < tableData.length; i++) {
        const customer = customers.find(
          (custom) => custom.customer_id === tableData[i].customer_id
        );
        if (customer) {
          tempData.push({
            customer_id: tableData[i].customer_id,
            client: customer.name,
            name: tableData[i].name,
            type_id: tableData[i].type_id,
            document_id: tableData[i].document_id,
            end_date: moment(tableData[i].end_date).format("DD/MM/YYYY"),
          });
        }
      }
      setLoading(false);
      setFilteredData(tempData);
      setData(tempData);
    });
  }

  function getFilteredChartData(filter) {
    if (filter.client) {
      const filteredClients = data.filter(
        (item) => item.client === filter.client
      );
      setFilteredData(filteredClients);
    }

    if (filter.contractTypes) {
      const filteredClients = data.filter(
        (item) => item.type_id === filter.contractTypes
      );
      setFilteredData(filteredClients);
    }

    if (!filter) {
      setFilteredData(data);
    }
  }

  useEffect(() => {
    generateTable();
  }, [tableData, contractTypes]);

  const handleCloseModal = () => {
    setPopoverVisibility(false);
    closeModal();
  };
  return (
    <Row gutter={[16, 16]} justify="space-between">
      <Col span={8}>
        <SearchInput
          onChange={(value) => {
            const filteredClients = data?.filter((item) =>
              item.client.toLowerCase().includes(value.toLowerCase())
            );
            setFilteredData(filteredClients);
          }}
          placeholder="Pesquisar cliente"
        />
      </Col>
      <Col>
        <Button
          type="text"
          icon={<CloseOutlined />}
          onClick={handleCloseModal}
        ></Button>
      </Col>
      <Col span={24}>
        <Table loading={loading} columns={columns} dataSource={filteredData} />
      </Col>
    </Row>
  );
}

export default NewContractsTable;
