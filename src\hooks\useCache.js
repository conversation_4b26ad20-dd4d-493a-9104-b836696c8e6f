import { useState, useEffect, useCallback, useRef } from 'react';
import { defaultCache, smartCache } from '../services/cacheService';

/**
 * Hook para usar cache de forma reativa
 * Automaticamente atualiza o componente quando o cache muda
 */
export const useCache = (key, fetcher, options = {}) => {
  const {
    params = {},
    ttl = 300000, // 5 minutos
    cache = defaultCache,
    enabled = true,
    dependencies = [],
    onSuccess,
    onError,
    staleWhileRevalidate = false
  } = options;

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);
  
  const fetcherRef = useRef(fetcher);
  const abortControllerRef = useRef(null);

  // Atualizar fetcher ref
  useEffect(() => {
    fetcherRef.current = fetcher;
  }, [fetcher]);

  /**
   * Busca dados com cache
   */
  const fetchData = useCallback(async (forceRefresh = false) => {
    if (!enabled) return;

    // Cancelar requisição anterior se existir
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    try {
      // Verificar cache primeiro
      if (!forceRefresh) {
        const cached = cache.get(key, params);
        if (cached) {
          setData(cached);
          setError(null);
          return cached;
        }
      }

      setLoading(true);
      setError(null);

      // Criar novo AbortController
      abortControllerRef.current = new AbortController();

      // Buscar dados
      const result = await fetcherRef.current({
        signal: abortControllerRef.current.signal,
        params
      });

      // Armazenar no cache
      cache.set(key, result, ttl, params);
      
      setData(result);
      setLastFetch(Date.now());
      
      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (err) {
      if (err.name !== 'AbortError') {
        setError(err);
        
        if (onError) {
          onError(err);
        }
      }
      throw err;
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [key, params, ttl, cache, enabled, onSuccess, onError]);

  /**
   * Revalida dados (busca novos dados)
   */
  const revalidate = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  /**
   * Invalida cache
   */
  const invalidate = useCallback(() => {
    cache.delete(key, params);
    
    // Se for smart cache, usar invalidação inteligente
    if (cache.invalidate) {
      cache.invalidate(key, params);
    }
  }, [cache, key, params]);

  /**
   * Atualiza dados no cache sem fazer nova requisição
   */
  const mutate = useCallback((newData, shouldRevalidate = true) => {
    if (typeof newData === 'function') {
      const currentData = cache.get(key, params) || data;
      newData = newData(currentData);
    }

    // Atualizar cache
    cache.set(key, newData, ttl, params);
    setData(newData);

    // Revalidar se solicitado
    if (shouldRevalidate) {
      fetchData(true);
    }

    return newData;
  }, [cache, key, params, ttl, data, fetchData]);

  // Buscar dados iniciais
  useEffect(() => {
    if (enabled) {
      fetchData();
    }
  }, [fetchData, enabled, ...dependencies]);

  // Configurar subscription para smart cache
  useEffect(() => {
    if (!cache.subscribe) return;

    const unsubscribe = cache.subscribe(key, () => {
      const newData = cache.get(key, params);
      if (newData !== null) {
        setData(newData);
      }
    }, params);

    return unsubscribe;
  }, [cache, key, params]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    lastFetch,
    revalidate,
    invalidate,
    mutate,
    isStale: lastFetch && (Date.now() - lastFetch) > ttl
  };
};

/**
 * Hook para cache de API com SWR
 */
export const useCachedAPI = (url, options = {}) => {
  const {
    method = 'GET',
    headers = {},
    body,
    ...cacheOptions
  } = options;

  const fetcher = useCallback(async ({ signal, params }) => {
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: body ? JSON.stringify(body) : undefined,
      signal
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }, [url, method, headers, body]);

  return useCache(url, fetcher, {
    ...cacheOptions,
    cache: smartCache
  });
};

/**
 * Hook para cache de dados com invalidação automática
 */
export const useSmartCache = (key, fetcher, options = {}) => {
  return useCache(key, fetcher, {
    ...options,
    cache: smartCache
  });
};

/**
 * Hook para gerenciar múltiplos caches
 */
export const useMultiCache = (queries) => {
  const results = {};
  
  for (const [queryKey, { key, fetcher, options = {} }] of Object.entries(queries)) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    results[queryKey] = useCache(key, fetcher, options);
  }

  const isLoading = Object.values(results).some(result => result.loading);
  const hasError = Object.values(results).some(result => result.error);
  const errors = Object.values(results)
    .map(result => result.error)
    .filter(Boolean);

  const revalidateAll = useCallback(() => {
    return Promise.all(
      Object.values(results).map(result => result.revalidate())
    );
  }, [results]);

  const invalidateAll = useCallback(() => {
    Object.values(results).forEach(result => result.invalidate());
  }, [results]);

  return {
    results,
    isLoading,
    hasError,
    errors,
    revalidateAll,
    invalidateAll
  };
};

/**
 * Hook para cache com dependências
 */
export const useDependentCache = (key, fetcher, dependencies = [], options = {}) => {
  const cache = smartCache;
  
  // Configurar dependências
  useEffect(() => {
    dependencies.forEach(dep => {
      cache.setDependency(key, dep);
    });
  }, [cache, key, dependencies]);

  return useCache(key, fetcher, {
    ...options,
    cache
  });
};

/**
 * Hook para cache com prefetch
 */
export const usePrefetchCache = (key, fetcher, options = {}) => {
  const result = useCache(key, fetcher, options);
  
  const prefetch = useCallback((prefetchKey, prefetchParams = {}) => {
    const cache = options.cache || defaultCache;
    
    // Verificar se já está em cache
    if (!cache.has(prefetchKey, prefetchParams)) {
      // Fazer prefetch em background
      fetcherRef.current({ params: prefetchParams })
        .then(data => {
          cache.set(prefetchKey, data, options.ttl, prefetchParams);
        })
        .catch(error => {
          console.warn('Erro no prefetch:', error);
        });
    }
  }, [options.cache, options.ttl]);

  return {
    ...result,
    prefetch
  };
};

export default useCache;
