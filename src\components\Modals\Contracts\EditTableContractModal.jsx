import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useSelector, shallowEqual } from "react-redux";
import {
  Modal,
  Button,
  Row,
  Col,
  Input,
  Select,
  Form,
  message,
  Tooltip,
  Tabs,
  Alert,
  DatePicker,
} from "antd";
import { MaskedInput } from "antd-mask-input";
import {
  EditOutlined,
  LoadingOutlined,
  MinusCircleOutlined,
} from "@ant-design/icons";
import { dynamoPost, dynamoPut } from "../../../service/apiDsmDynamo";
import { otrsGet, otrsPost, otrsPut } from "../../../service/apiOtrs";

import { updateConsumptionHourContract } from "../../../provider/otrs-calculate-hours-provider";
import { CreateWallet } from "../../../components/Modals/Contracts/CreateWallet";
import * as controller from "../../../controllers/contracts/contract-controller";
import {
  editContractModalTabs,
  editContractsFormFields,
  fullWidthFields,
} from "./constants/editContractsFormFields";
import {
  formatCreateContractOTRS,
  formatEditContractDSM,
  formatEditContractOTRS,
  formatEditStateContract,
  formatFormValuesSetter,
} from "./controllers/editContract";
import { removeEspecialCharacters } from "../../../hooks/removeSpecialChars";
import { formRules } from "./constants/formRules";
import moment from "moment";
import { v4 } from "uuid";
import { moneyMask } from "../../../utils/money-maks";
import {
  differenceInCalendarDays,
  differenceInCalendarMonths,
  parse,
} from "date-fns";

export const EditTableContractModal = (props) => {
  const { activeContracts, inactiveContracts, wallets } = useSelector(
    (state) => state.contract,
    shallowEqual
  );
  const { client, getCustomerContracts, permissions } = props;
  const [disableSignedDate, setDisableSignedDate] = useState(false);
  const [loadingForm, setLoadingForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState();
  const [show, setShow] = useState(false);
  const [edit, setEdit] = useState("");
  const [form] = Form.useForm();

  const fieldEdit = [];
  let [fieldsBasedOnPermission, setFieldsBasedOnPermission] = useState([]);
  const [contract, setContract] = useState(null);
  const [showEdpCommitment, setShowEdpCommitment] = useState(false);
  const [subItemsEdpCommitment, setSubItemsEdpCommitment] = useState([]);

  const editContract = async (data) => {
    console.log("🎯 FUNÇÃO editContract CHAMADA!");
    console.log("🔄 Iniciando edição do contrato com dados:", data);

    if (
      showEdpCommitment &&
      (subItemsEdpCommitment.length === 0 ||
        subItemsEdpCommitment.some((edp) => {
          return (
            edp.commitmentValue === "" ||
            edp.commitmentRange === null ||
            edp.commitmentRange?.length === 0 ||
            edp.commitmentRange[0] === null ||
            edp.commitmentRange[1] === null
          );
        }))
    ) {
      message.error("Preencha o campo commitment EDP corretamente, por favor.");
      return;
    }
    let checkDateFormats = Object.keys(data).map((key) => {
      if (key?.includes("date") && data[key]?.includes("_") && key) {
        let fieldLabel = editContractsFormFields.find(
          (field) => field.key === key
        ).label;

        message.error(
          `Preencha o campo ${fieldLabel} corretamente, por favor.`
        );

        return key;
      }
      return null;
    });
    if (checkDateFormats.some((e) => e)) return;
    if (data.value_addon) data.value = data.value_addon + " " + data.value;
    if (data.discount === "edp") data.edp_commitment = subItemsEdpCommitment;
    if (data.signed === "Não" && data.signature_date)
      delete data.signature_date;

    const diff = differenceInCalendarDays(
      parse(data.expected_close_date, "dd/MM/yyyy", new Date()),
      parse(data.expected_start_date, "dd/MM/yyyy", new Date())
    );

    if (diff < 1)
      return message.error(
        "A data final do contrato deve ser posterior a data inicial"
      );

    setLoading(true);
    console.log("🔄 Iniciando processo de salvamento...");

    try {
      try {
        console.log("✅ Verificando contratos duplicados...");
        const contracts = [...activeContracts, ...inactiveContracts];
        const contractWithSameName = contracts.find(
          (c) =>
            c.name.toLowerCase() === data.name.toLowerCase() &&
            c.id !== contract.id
        );

        if (contractWithSameName) {
          console.log("❌ Contrato com nome duplicado encontrado");
          setLoading(false);
          return message.error("Já existe um contrato com esse nome");
        }
        console.log("✅ Verificação de duplicados passou");

        console.log("🔄 Buscando contrato no OTRS...");
        const contract_otrs = await otrsGet("read/contract/id/" + data.itsm_id);
        let createOTRSContractBodyRequest = formatCreateContractOTRS(
          data,
          contract,
          client
        );

        if (!contract_otrs.data?.id) {
          console.log("⚠️ Contrato não encontrado no OTRS, criando novo...");
          message.success(
            "Contrato não encontrado no OTRS, efetuando a criação agora!"
          );

          await otrsPost("create/contract", createOTRSContractBodyRequest)
            .then((response) => {
              console.log("✅ Contrato criado no OTRS:", response.data.contract_id);
              data["itsm_id"] = response.data.contract_id;
            })
            .catch((error) => {
              console.log("❌ Erro ao criar contrato no OTRS:", error);
              setLoading(false);
              return message.error(
                "Ocorreu um erro ao tentar criar o contrato no OTRS :("
              );
            });
        } else {
          try {
            console.log("🔄 Atualizando contrato existente no OTRS...");
            let updateOTRSContractBodyRequest = formatEditContractOTRS(
              data,
              contract,
              client
            );
            await otrsPut(
              "update/contract/" + data.itsm_id,
              updateOTRSContractBodyRequest
            );
            console.log("✅ Contrato atualizado no OTRS com sucesso");
          } catch (error) {
            console.log("❌ Erro ao atualizar contrato no OTRS:", error);
            setLoading(false);
            return message.error(
              "Ocorreu um erro ao tentar atualizar o contrato no OTRS :("
            );
          }
        }
      } catch (error) {
        setLoading(false);
        console.log(error);
        return message.error(
          "Ocorreu um erro ao tentar atualizar o contrato no OTRS :("
        );
      }

      try {
        console.log("🔄 Atualizando contrato no DSM...");
        let DSMBodyRequest = formatEditContractDSM(data);
        DSMBodyRequest = {
          ...DSMBodyRequest,
          customer_id: contract.customer_id,
          customer_itsm: contract.customer_itsm,
        };
        console.log("📝 Dados para atualização no DSM:", DSMBodyRequest);

        await dynamoPut(
          `${process.env.REACT_APP_STAGE}-contracts`,
          contract.id,
          DSMBodyRequest
        );
        console.log("✅ Contrato atualizado no DSM com sucesso");

        console.log("🔄 Atualizando horas de consumo...");
        updateConsumptionHourContract({
          contractID: DSMBodyRequest.identifications.itsm_id,
        });

        console.log("🔄 Atualizando contratos do cliente...");
        await getCustomerContracts();
        console.log("✅ Contratos do cliente atualizados");
      } catch (error) {
        console.log("❌ Erro ao atualizar contrato no DSM:", error);
        setLoading(false);
        return message.error(
          "O contrato foi atualizado no OTRS, mas ocorreu um erro ao tentar atualizar o contrato no DSM :("
        );
      }

      const username = localStorage.getItem("@dsm/username");

      let arr = [];

      const newArr = uniqByKeepLast(fieldEdit, (e) => e.split(" ")[0]);

      newArr.forEach((e) => {
        return arr.push(e[1]);
      });

      const title = "Contrato Editado";

      const description = `${username} editou o contrato ${contract.name}, com as seguintes alterações: ${arr}.`;

      dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
        username: username,
        name: title,
        description: description,
        created_at: new Date(),
        updated_at: new Date(),
      });

      console.log("🔄 Atualizando listas de contratos...");
      await controller.getActiveContracts();
      controller.getInactiveContracts();
      console.log("✅ Listas de contratos atualizadas");

      console.log("🎉 Edição do contrato concluída com sucesso!");
      message.success("Sucesso na edição do contrato!");
      setLoading(false);
      setShowModal(false);
    } catch (err) {
      console.log("❌ Erro inesperado na edição do contrato:", err);
      setLoading(false);
      message.error(
        "Ops! Ocorreu um erro inesperado na edição deste contrato..."
      );
    }
  };

  function uniqByKeepLast(data, key) {
    return [...new Map(data.map((x) => [key(x), x]).values())];
  }

  const getContract = async () => {
    setLoadingForm(true);
    form.resetFields();

    const contractID = props.contract.id;
    const contractInfo = await controller.getContract(contractID);
    setContract(contractInfo);
    if (contractInfo.signed === "Não") setDisableSignedDate(true);
    setEdit(formatEditStateContract(contractInfo));

    form.setFieldsValue(formatFormValuesSetter(contractInfo));
    if (contractInfo.discount === "edp") {
      setSubItemsEdpCommitment(contractInfo.edp_commitment);
      setShowEdpCommitment(true);
    }

    setLoadingForm(false);
  };

  function getChange(key) {
    for (let i = 0; i < Object.entries(edit).length; i++) {
      if (Object.entries(edit)[i][0] === key) {
        return Object.values(edit)[i];
      }
    }
  }

  const getWindowDimensions = () => {
    const { innerWidth: width, innerHeight: height } = window;
    return { width, height };
  };

  const useWindowDimensions = () => {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions()
    );

    useEffect(() => {
      const handleResize = () => setWindowDimensions(getWindowDimensions());

      window.addEventListener("resize", handleResize);

      return () => window.removeEventListener("resize", handleResize);
    }, []);

    return windowDimensions;
  };

  const { width } = useWindowDimensions();

  const removeDuplicates = (array) => {
    return array.filter((a, b) => {
      return array.findIndex((element) => element.name === a.name) === b;
    });
  };

  const allWallets = useMemo(() => {
    if (!wallets) return [];
    let uniqueWallets = removeDuplicates(wallets);
    return uniqueWallets.map((wallet) => {
      return {
        label: wallet.name,
        value: wallet.name,
      };
    });
  }, [wallets]);

  function replaceArrayElements(array) {
    const elementsParaOFinal = [];
    const elementsParaOInicio = [];

    for (const element of array) {
      if (
        !permissions.find(
          (permission) => permission.code === element.permissionCode
        )
      ) {
        elementsParaOFinal.push(element);
      } else {
        elementsParaOInicio.push(element);
      }
    }

    const novoArray = elementsParaOInicio.concat(elementsParaOFinal);
    setFieldsBasedOnPermission(novoArray);
  }

  const verifyRequiredFieldsBasedOnPermission = () => {
    let verifiedFields = editContractsFormFields.filter(
      (field) =>
        field.required &&
        permissions.find((e) => e.code === field.permissionCode)
    );
    return verifiedFields;
  };

  const filterFieldsBasedOnPermission = (requiredFields) => {
    let filteredFields = requiredFields
      .map((field) => {
        if (field.key === "signature_date" && disableSignedDate) return null;
        if (field.key === "edp_commitment" && !showEdpCommitment) {
          field.required = false;
          return null;
        }
        if (!form.getFieldValue(field.key)) {
          return field;
        }
        return null;
      })
      .filter((field) => field);

    return filteredFields;
  };

  const checkForEmptyRequiredFields = () => {
    console.log("🔍 Iniciando verificação de campos obrigatórios...");
    const requiredFields = verifyRequiredFieldsBasedOnPermission();
    console.log("📋 Campos obrigatórios encontrados:", requiredFields);
    let checkFields = filterFieldsBasedOnPermission(requiredFields);
    console.log("🔍 Campos vazios encontrados:", checkFields);

    if (checkFields.length > 0) {
      let firstEmptyItem = checkFields.shift();
      let firstEmptyItemTabName = editContractModalTabs.find(
        (tab) => tab.key === firstEmptyItem?.tabKey
      ).title;
      console.log("❌ Campo vazio encontrado:", firstEmptyItem, "na aba:", firstEmptyItemTabName);
      message.error(
        `Preencha o campo
          ${firstEmptyItem?.label}
          na aba
              ${firstEmptyItemTabName}
          `
      );
      return false; // Retorna false se há campos obrigatórios vazios
    }
    console.log("✅ Todos os campos obrigatórios estão preenchidos");
    return true; // Retorna true se todos os campos obrigatórios estão preenchidos
  };

  const handleOpenModal = async () => {
    setShowModal(true);
    await getContract();
    replaceArrayElements(editContractsFormFields);
  };

  return (
    <>
      <CreateWallet
        setShow={(state) => setShow(state)}
        tables={wallets}
        form={form}
        show={show}
      />
      <Tooltip title="Editar informações do contrato">
        <Button type="text" onClick={handleOpenModal}>
          <EditOutlined />
        </Button>
      </Tooltip>
      <Modal
        title="Editar Contratos"
        width={width > 1000 ? "50%" : "100%"}
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={[
          <Button key={"closeButton"} onClick={() => setShowModal(false)}>
            Fechar
          </Button>,
          <Button
            key={"submitButton"}
            type="primary"
            loading={loading}
            onClick={async () => {
              console.log("🔘 Botão Salvar clicado");

              try {
                // Primeiro, valida o formulário do Ant Design
                console.log("🔄 Validando formulário do Ant Design...");
                const values = await form.validateFields();
                console.log("✅ Validação do Ant Design passou:", values);

                // Depois, faz a validação personalizada
                console.log("🔄 Fazendo validação personalizada...");
                const customValidationPassed = checkForEmptyRequiredFields();
                console.log("✅ Validação personalizada passou:", customValidationPassed);

                if (customValidationPassed) {
                  console.log("🔄 Chamando editContract diretamente com os valores...");
                  // Chama diretamente a função editContract em vez de form.submit()
                  await editContract(values);
                } else {
                  console.log("❌ Validação personalizada falhou, não submetendo formulário");
                }
              } catch (errorInfo) {
                console.log("❌ Erro na validação do Ant Design:", errorInfo);
                console.log("📋 Campos com erro:", errorInfo.errorFields);

                // Mostra mensagem de erro mais específica
                if (errorInfo.errorFields && errorInfo.errorFields.length > 0) {
                  const firstError = errorInfo.errorFields[0];
                  const fieldName = firstError.name[0];
                  const errorMessage = firstError.errors[0];
                  message.error(`Campo "${fieldName}": ${errorMessage}`);
                } else {
                  message.error("Por favor, preencha todos os campos obrigatórios.");
                }
              }
            }}
          >
            Salvar
          </Button>,
        ]}
      >
        <Form
          onValuesChange={(data) => {
            if (
              Object.keys(data)[0] === "discount" &&
              Object.values(data)[0] === "edp"
            ) {
              setShowEdpCommitment(true);
            } else if (
              Object.keys(data)[0] === "discount" &&
              Object.values(data)[0] === "spp"
            ) {
              setShowEdpCommitment(false);
              setSubItemsEdpCommitment([]);
            }
            fieldEdit.push(
              `${Object.keys(data)[0]} alterado de: ${getChange(
                Object.keys(data)[0]
              )} para ${Object.values(data)[0]}`
            );
          }}
          form={form}
          onFinish={editContract}
          onFinishFailed={(errorInfo) => {
            console.log("❌ Falha na validação do formulário:", errorInfo);
          }}
          name="control-hooks"
          layout="vertical"
          requiredMark={true}
        >
          <Tabs
            defaultActiveKey="general_info"
            type="card"
            items={editContractModalTabs.map((tab) => {
              return {
                key: tab.key,
                disabled: !permissions.some(
                  (permission) => permission.code === tab.permissionCode
                ),
                label: tab.title,
                children: (
                  <Row gutter={16} justify="center">
                    {loadingForm ? (
                      <LoadingOutlined />
                    ) : (
                      <RenderFormFields
                        tab={tab}
                        fieldsBasedOnPermission={fieldsBasedOnPermission}
                        permissions={permissions}
                        disableSignedDate={disableSignedDate}
                        setDisableSignedDate={setDisableSignedDate}
                        fullWidthFields={fullWidthFields}
                        allWallets={allWallets}
                        setShow={setShow}
                        form={form}
                        contract={contract}
                        showEdpCommitment={showEdpCommitment}
                        subItemsEdpCommitment={subItemsEdpCommitment}
                        setSubItemsEdpCommitment={setSubItemsEdpCommitment}
                      />
                    )}
                  </Row>
                ),
              };
            })}
          />
        </Form>
      </Modal>
    </>
  );
};

const RenderFormFields = (props) => {
  const {
    tab,
    fieldsBasedOnPermission,
    permissions,
    disableSignedDate,
    setDisableSignedDate,
    fullWidthFields,
    allWallets,
    setShow,
    form,
    contract,
    showEdpCommitment,
    subItemsEdpCommitment,
    setSubItemsEdpCommitment,
  } = props;
  const { Option } = Select;

  const removeEmptyMaskedInputs = (formField, e) => {
    let value = removeEspecialCharacters(e.target.value);
    if (value === "") {
      form.setFieldsValue({ [formField.key]: undefined });
    }
    return;
  };

  const renderAddonIcon = useCallback(
    (field) => {
      const Icon = field?.AddonIcon;
      return (
        <Input
          type={field.dataType}
          placeholder={field.placeholder}
          disabled={true}
          addonBefore={
            field?.AddonIcon ? (
              <Icon
                onClick={() =>
                  contract.s3FolderNotes &&
                  window.open(contract.s3FolderNotes, "_blank")
                }
              />
            ) : (
              <></>
            )
          }
        ></Input>
      );
    },
    [contract]
  );

  const formatTypeFormFields = (fields) => {
    switch (fields.inputType) {
      case "text":
        if (fields.dataType === "link") {
          return renderAddonIcon(fields);
        }
        return (
          <Input
            type={fields.dataType}
            placeholder={fields.placeholder}
            disabled={fields?.disabled}
            addonBefore={
              fields.addonChildren ? (
                <Form.Item name={fields.key + "_addon"} noStyle>
                  {fields.addonChildren}
                </Form.Item>
              ) : null
            }
          ></Input>
        );
      case "select":
        if (fields.key === "squad") {
          return (
            <Select
              placeholder="Carteira"
              onChange={(e) => {
                if (e === "Outro") {
                  setShow(true);
                }
              }}
            >
              {allWallets.map((wallet, index) => {
                return (
                  <Option key={index} value={wallet.value}>
                    {wallet.label}
                  </Option>
                );
              })}
              <Option value="Outro">Outro</Option>
            </Select>
          );
        } else {
          return (
            <Select
              onChange={(e) => {
                if (fields.key === "signed") {
                  if (e === "Sim") {
                    setDisableSignedDate(false);
                  } else {
                    setDisableSignedDate(true);
                  }
                }
              }}
              placeholder={fields.placeholder}
              options={fields.options}
            ></Select>
          );
        }
      case "textArea":
        return (
          <Input.TextArea placeholder={fields.placeholder}></Input.TextArea>
        );
      default:
        if (disableSignedDate && fields.key === "signature_date") {
          return (
            <Alert message="Contrato não assinado" type="warning" showIcon />
          );
        } else if (fields.key.includes("edp_commitment")) {
          return (
            <EditEDPCommitment
              showEdpCommitment={showEdpCommitment}
              subItemsEdpCommitment={subItemsEdpCommitment}
              setSubItemsEdpCommitment={setSubItemsEdpCommitment}
              form={form}
            />
          );
        } else {
          return (
            <MaskedInput
              onChange={(e) => {
                removeEmptyMaskedInputs(fields, e);
              }}
              placeholder={fields.placeholder}
              mask="00/00/0000"
            />
          );
        }
    }
  };

  return fieldsBasedOnPermission.map((field, index) => {
    return (
      <Col
        key={index}
        span={fullWidthFields.find((e) => e === field.key) ? 24 : 12}
      >
        <Form.Item
          key={index}
          id={field.key}
          hidden={
            field.tabKey === tab.key &&
            permissions.find(
              (permission) => permission.code === field.permissionCode
            )
              ? false
              : true
          }
          label={
            field.key === "edp_commitment" && !showEdpCommitment
              ? ""
              : field.label
          }
          name={field.key}
          rules={formRules(field, disableSignedDate, permissions)}
        >
          {formatTypeFormFields(field)}
        </Form.Item>
      </Col>
    );
  });
};

const EditEDPCommitment = ({
  subItemsEdpCommitment,
  setSubItemsEdpCommitment,
  showEdpCommitment,
  form,
}) => {
  const { RangePicker } = DatePicker;
  const [errorFields, setErrorFields] = useState([]);
  const formatCommitments = (commitments) => {
    return commitments.map((commitment) => {
      const { startDate, endDate } = commitment.commitmentRange || {};
      return {
        ...commitment,
        commitmentRange: [
          moment(startDate || commitment?.commitmentRange?.[0], "YYYY-MM"),
          moment(endDate || commitment?.commitmentRange?.[1], "YYYY-MM"),
        ],
      };
    });
  };

  const handleCommitmentValueChange = (e) => {
    const value = e.target.value;
    const formattedValue = moneyMask(value);
    e.currentTarget.value = formattedValue ? formattedValue : "";
  };

  const handleValuesChange = () => {
    setErrorFields((prevErrors) => {
      const indexWithError = prevErrors.findIndex((error) => error);
      if (indexWithError !== -1) {
        handleResetRangePicker(indexWithError);
      } else {
        const commitments = form.getFieldValue("subItemsEdpCommitment");
        setSubItemsEdpCommitment(commitments);
      }
      return prevErrors;
    });
  };

  const checkPeriodOverlap = (date, period) => {
    const [startA, endA] = date;
    const [startB, endB] = period;

    return startA <= endB && endA >= startB;
  };

  const checkPeriodAlreadySelected = (index, date) => {
    const commitments = form.getFieldValue("subItemsEdpCommitment");
    return commitments.some((commitment, i) => {
      return (
        i !== index && checkPeriodOverlap(date, commitment.commitmentRange)
      );
    });
  };

  const handleResetRangePicker = (index) => {
    const commitments = form.getFieldValue("subItemsEdpCommitment");
    const updatedCommitments = commitments.map((commitment, i) => {
      return i === index
        ? { ...commitment, commitmentRange: [null, null] }
        : commitment;
    });
    setSubItemsEdpCommitment(updatedCommitments);
  };

  const setErrorField = () => {
    const commitments = form.getFieldValue("subItemsEdpCommitment");
    const errors = commitments.map((commitment, i) => {
      return checkPeriodAlreadySelected(i, commitment.commitmentRange);
    });
    setErrorFields(errors);
  };

  useEffect(() => {
    setErrorField();
  }, []);

  const removeCommitment = (index) => {
    const commitments = form.getFieldValue("subItemsEdpCommitment");
    const updatedCommitments = commitments.filter((_, i) => i !== index);
    setSubItemsEdpCommitment(updatedCommitments);
    handleValuesChange();
    setErrorField();
  };

  return (
    <>
      <Form.List
        name="subItemsEdpCommitment"
        initialValue={formatCommitments(subItemsEdpCommitment)}
      >
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }, index) => (
              <div key={key} hidden={!showEdpCommitment}>
                <Row align="middle" justify="space-between" gutter={[8, 0]}>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "commitmentValue"]}
                      label="Valor do commitment"
                      onChange={handleValuesChange}
                      required={showEdpCommitment}
                    >
                      <Input
                        placeholder="Valor do commitment"
                        onInput={handleCommitmentValueChange}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={11}>
                    <Form.Item
                      {...restField}
                      name={[name, "commitmentRange"]}
                      label="Período do commitment"
                      required={showEdpCommitment}
                      validateStatus={errorFields[index] ? "error" : ""}
                      help={errorFields[index] ? "Período já selecionado" : ""}
                    >
                      <RangePicker
                        picker="month"
                        onChange={(date) => {
                          if (date && checkPeriodAlreadySelected(index, date)) {
                            setErrorFields((prevErrors) => {
                              const updatedErrors = [...prevErrors];
                              updatedErrors[index] = true;
                              return updatedErrors;
                            });
                          } else {
                            setErrorFields((prevErrors) => {
                              const updatedErrors = [...prevErrors];
                              updatedErrors[index] = false;
                              return updatedErrors;
                            });
                          }
                          handleValuesChange();
                        }}
                        style={{
                          width: "100%",
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={0.5}>
                    <MinusCircleOutlined
                      onClick={() => {
                        remove(name);
                        removeCommitment(index);
                      }}
                    />
                  </Col>
                </Row>
              </div>
            ))}

            <Button
              type="dashed"
              key="addCommitment"
              onClick={() => {
                const newCommitment = {
                  commitmentRange: [],
                  commitmentValue: "",
                  id: v4(),
                };
                add(newCommitment);
                handleValuesChange();
              }}
              hidden={!showEdpCommitment}
              block
            >
              + Adicionar commitment
            </Button>
          </>
        )}
      </Form.List>
    </>
  );
};
